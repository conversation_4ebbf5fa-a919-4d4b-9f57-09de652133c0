# FastAPI Demo Application

A modern, production-ready FastAPI application with comprehensive authentication, user management, and admin features. This project follows clean architecture principles and industry best practices for Python web development with enterprise-grade features.

## 🏗️ Project Structure

```
fast_api_demo/
├── app/                     # 📦 Main application package
│   ├── __init__.py         # Package initialization with version info
│   ├── main.py             # 🚀 FastAPI application entry point
│   ├── api/                # 🌐 API layer
│   │   ├── __init__.py     # API package with common dependencies
│   │   ├── deps.py         # 🔗 Dependency injection functions
│   │   ├── versioning.py   # 📋 API versioning logic
│   │   └── routes/         # 🛣️ API route handlers
│   │       ├── __init__.py # Routes package with router exports
│   │       ├── auth.py     # 🔐 Authentication endpoints
│   │       ├── users.py    # 👥 User management endpoints
│   │       ├── admin.py    # 👑 Admin management endpoints
│   │       └── health.py   # ❤️ Health check endpoints
│   ├── core/               # ⚙️ Core utilities and configuration
│   │   ├── __init__.py     # Core package with common imports
│   │   ├── config.py       # 📋 Application settings and configuration
│   │   ├── security.py     # 🔒 Security utilities (JWT, password hashing)
│   │   ├── logging.py      # 📝 Logging configuration
│   │   ├── middleware.py   # 🔄 Custom middleware (CORS, security, rate limiting)
│   │   ├── monitoring.py   # 📊 Application monitoring and metrics
│   │   ├── exceptions.py   # ⚠️ Custom exception classes
│   │   ├── validation.py   # ✅ Input validation utilities
│   │   ├── cache.py        # 💾 Caching utilities
│   │   └── utils.py        # 🛠️ General utility functions
│   ├── db/                 # 🗄️ Database layer
│   │   ├── __init__.py     # Database package with connection exports
│   │   ├── base.py         # 🔌 Database connection and session management
│   │   └── models/         # 📊 SQLAlchemy ORM models
│   │       ├── __init__.py # Models package with all model imports
│   │       ├── base.py     # 🏗️ Base model class with common fields
│   │       ├── user.py     # 👤 User model
│   │       └── admin.py    # 👑 Admin model
│   ├── schemas/            # 📝 Pydantic models for validation
│   │   ├── __init__.py     # Schemas package with validation models
│   │   ├── base.py         # 🏗️ Base schemas and mixins
│   │   ├── user.py         # 👤 User schemas (create, update, response)
│   │   └── admin.py        # 👑 Admin schemas
│   └── services/           # 💼 Business logic layer
│       ├── __init__.py     # Services package
│       ├── base.py         # 🏗️ Base service class
│       ├── auth_service.py # 🔐 Authentication business logic
│       ├── user_service.py # 👥 User management business logic
│       └── admin_service.py# 👑 Admin management business logic
├── alembic/                # 🔄 Database migrations
│   ├── versions/           # Migration files
│   ├── env.py             # Alembic environment configuration
│   └── script.py.mako     # Migration template
├── logs/                   # 📋 Application logs
│   ├── app.log            # General application logs
│   └── error.log          # Error logs
├── docker-compose.yml      # 🐳 Development environment setup
├── docker-compose.prod.yml # 🚀 Production environment setup
├── Dockerfile             # 🐳 Development Docker image
├── Dockerfile.prod        # 🚀 Production Docker image (multi-stage)
├── nginx.conf             # 🌐 Nginx reverse proxy (development)
├── nginx.prod.conf        # 🚀 Production Nginx with SSL
├── alembic.ini            # ⚙️ Alembic configuration
├── requirements.txt       # 📦 Python dependencies
├── .env.example           # 📋 Environment variables template
├── .gitignore             # 🚫 Git ignore rules
├── .dockerignore          # 🐳 Docker ignore rules
└── README.md              # 📖 Project documentation
```

## 🚀 Features

### 🔐 **Authentication & Security**
- **JWT Authentication**: Secure token-based authentication with refresh tokens
- **User Management**: Complete user registration, login, and profile management
- **Admin System**: Role-based access control with admin privileges
- **Password Security**: Bcrypt hashing with strength validation
- **Rate Limiting**: Configurable request rate limiting
- **Security Headers**: CORS, XSS protection, and security middleware
- **Input Validation**: Comprehensive Pydantic validation

### 🏗️ **Architecture & Development**
- **Clean Architecture**: Separation of concerns with distinct layers
- **Dependency Injection**: FastAPI's dependency system for loose coupling
- **Database Integration**: SQLAlchemy ORM with PostgreSQL/SQLite support
- **Database Migrations**: Alembic for version-controlled schema changes
- **Monitoring**: Built-in application metrics and health checks
- **Logging**: Structured logging with rotation and different levels
- **Exception Handling**: Centralized error handling with custom exceptions

### 🚀 **Production Ready**
- **Docker Support**: Multi-stage Docker builds for development and production
- **Nginx Integration**: Reverse proxy with SSL termination
- **Health Checks**: Comprehensive health monitoring endpoints
- **Environment Configuration**: Environment-specific settings management
- **Caching**: Redis integration for session and data caching
- **Documentation**: Auto-generated API documentation with Swagger UI

## 🛠️ Technology Stack

### **Backend Framework**
- **FastAPI** - Modern, fast web framework for building APIs
- **Uvicorn** - ASGI server for development
- **Gunicorn** - WSGI server for production

### **Database & ORM**
- **SQLAlchemy 2.0** - Modern Python SQL toolkit and ORM
- **Alembic** - Database migration tool
- **PostgreSQL** - Production database (Docker containerized)
- **SQLite** - Development database option

### **Authentication & Security**
- **python-jose** - JWT token handling
- **passlib** - Password hashing with bcrypt
- **python-multipart** - Form data handling

### **Validation & Serialization**
- **Pydantic 2.0** - Data validation using Python type annotations
- **Pydantic Settings** - Environment-based configuration management

### **Caching & Session Management**
- **Redis** - In-memory data structure store for caching

### **Monitoring & Logging**
- **psutil** - System and process monitoring
- **python-json-logger** - Structured JSON logging

### **Development & Deployment**
- **Docker & Docker Compose** - Containerization
- **Nginx** - Reverse proxy and load balancer
- **pytest** - Testing framework (ready for implementation)
- **Alembic** - Database migrations

## 📋 Prerequisites

- Python 3.8+
- pip or poetry for package management
- PostgreSQL (optional, SQLite works for development)

## 🔧 Installation & Setup

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd fast-api
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Environment configuration**
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

5. **Database setup**
   ```bash
   # Run database migrations
   alembic upgrade head
   ```

6. **Run the application**
   ```bash
   uvicorn app.main:app --reload
   ```

## 🌐 API Endpoints

All API endpoints are prefixed with `/api/v1` for versioning.

### 🔐 **Authentication** (`/api/v1/auth`)
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh JWT token
- `GET /api/v1/auth/me` - Get current user profile
- `PUT /api/v1/auth/me` - Update current user profile
- `POST /api/v1/auth/change-password` - Change user password

### 👥 **User Management** (`/api/v1/users`)
- `GET /api/v1/users/` - Get all users (admin only)
- `GET /api/v1/users/{user_id}` - Get specific user
- `PUT /api/v1/users/{user_id}` - Update user (admin only)
- `DELETE /api/v1/users/{user_id}` - Delete user (admin only)
- `POST /api/v1/users/{user_id}/activate` - Activate user (admin only)
- `POST /api/v1/users/{user_id}/deactivate` - Deactivate user (admin only)

### 👑 **Admin Management** (`/api/v1/admins`)
- `POST /api/v1/admins/` - Create admin user (admin only)
- `GET /api/v1/admins/` - Get all admin users (admin only)
- `GET /api/v1/admins/{admin_id}` - Get specific admin (admin only)
- `PUT /api/v1/admins/{admin_id}` - Update admin (admin only)
- `DELETE /api/v1/admins/{admin_id}` - Delete admin (admin only)

### ❤️ **Health & Monitoring** (`/api/v1/health`)
- `GET /health` - Basic health check
- `GET /api/v1/health/detailed` - Detailed health information
- `GET /api/v1/health/readiness` - Kubernetes readiness probe
- `GET /api/v1/health/liveness` - Kubernetes liveness probe
- `GET /api/v1/metrics` - Application metrics (Prometheus format)

## 📚 API Documentation

Once the server is running, visit:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 🐳 Docker Development

### Quick Start with Docker Compose
```bash
# Clone the repository
git clone <your-repo-url>
cd fast_api_demo

# Start all services (PostgreSQL, Redis, FastAPI, Nginx)
docker-compose up -d

# View logs
docker-compose logs -f app

# Stop services
docker-compose down
```

### Services Included
- **FastAPI App**: http://localhost:8000
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379
- **Nginx**: http://localhost (proxies to FastAPI)

## 🚀 Production Deployment

### Production Docker Setup
```bash
# Create production environment file
cp .env.example .env.prod
# Edit .env.prod with production values

# Deploy with production compose
docker-compose -f docker-compose.prod.yml up -d

# View production logs
docker-compose -f docker-compose.prod.yml logs -f
```

### Production Features
- **Multi-stage Docker builds** for optimized images
- **Nginx with SSL termination** and security headers
- **Health checks** and automatic restarts
- **Resource limits** and scaling configuration
- **Structured logging** with log rotation

## 🧪 Testing

**Note**: Test suite implementation is planned for the next development phase.

```bash
# Install test dependencies (when available)
pip install -r requirements-test.txt

# Run test suite
pytest

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test categories
pytest tests/test_api/          # API tests
pytest tests/test_services/     # Service tests
pytest tests/test_core/         # Core functionality tests
```

## 🔒 Environment Variables

Create a `.env` file based on `.env.example`:

```bash
cp .env.example .env
```

### Required Variables
```env
# Application
PROJECT_NAME=FastAPI Demo
VERSION=1.0.0
DEBUG=False
SECRET_KEY=your-super-secure-secret-key-at-least-32-characters-long

# Database
DATABASE_URL=postgresql+psycopg2://user:password@localhost:5432/dbname
# For SQLite: sqlite:///./fastapi_demo.db

# Security & Authentication
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_PERIOD=60

# Redis (Optional - for caching)
REDIS_URL=redis://localhost:6379/0

# Logging
LOG_LEVEL=INFO
```

### Production Variables
```env
# Production-specific settings
DEBUG=False
LOG_LEVEL=WARNING
CORS_ORIGINS=https://yourdomain.com
DATABASE_URL=postgresql+psycopg2://user:password@db:5432/production_db
```

## 🏃‍♂️ Quick Start Guide

### Development Mode
1. **Start the server**: `uvicorn app.main:app --reload`
2. **Open browser**: Navigate to http://localhost:8000/docs
3. **Register a user**: Use the `/api/v1/auth/register` endpoint
4. **Login**: Use the `/api/v1/auth/login` endpoint to get a token
5. **Authorize**: Click "Authorize" in Swagger UI and enter your token
6. **Explore APIs**: Use the user and admin management endpoints

### Docker Mode
1. **Start services**: `docker-compose up -d`
2. **Check health**: Visit http://localhost:8000/health
3. **Access API**: Navigate to http://localhost:8000/docs
4. **Monitor logs**: `docker-compose logs -f app`

## 📊 Monitoring & Health Checks

### Health Endpoints
- **Basic Health**: `GET /health` - Simple health status
- **Detailed Health**: `GET /api/v1/health/detailed` - System information
- **Readiness**: `GET /api/v1/health/readiness` - Service readiness
- **Liveness**: `GET /api/v1/health/liveness` - Service liveness

### Metrics & Monitoring
- **Application Metrics**: `GET /api/v1/metrics` - Prometheus-style metrics
- **Request Logging**: Structured logs with request IDs
- **Error Tracking**: Centralized exception handling
- **Performance Monitoring**: Response time and throughput metrics

## 🔧 Development

### Code Quality Tools
```bash
# Format code
black app/
isort app/

# Type checking
mypy app/

# Linting
flake8 app/
```

### Database Operations
```bash
# Create migration
alembic revision --autogenerate -m "description"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1

# Check migration status
alembic current
```

## 🐛 Troubleshooting

### Common Issues

**Import Errors**
- Ensure all `__init__.py` files are present
- Check Python path configuration
- Verify virtual environment activation

**Database Connection Issues**
- Check DATABASE_URL format
- Ensure database server is running
- Verify credentials and permissions

**Docker Issues**
- Check Docker daemon is running
- Verify port availability (8000, 5432, 6379)
- Review docker-compose logs

### Debug Mode
```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
export DEBUG=True

# Run with detailed error messages
uvicorn app.main:app --reload --log-level debug
```

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** following the coding standards
4. **Add tests** for new functionality
5. **Run quality checks**:
   ```bash
   black app/                    # Format code
   isort app/                    # Sort imports
   mypy app/                     # Type checking
   pytest                        # Run tests (when available)
   ```
6. **Commit your changes**: `git commit -m 'Add amazing feature'`
7. **Push to the branch**: `git push origin feature/amazing-feature`
8. **Submit a pull request**

### Development Guidelines
- Follow PEP 8 style guidelines
- Add type hints to all functions
- Write comprehensive docstrings
- Maintain test coverage above 80%
- Update documentation for new features

## 📚 Additional Resources

- **[Deployment Guide](DEPLOYMENT.md)** - Production deployment instructions
- **[Exception Handling Guide](EXCEPTION_HANDLING_GUIDE.md)** - Error handling patterns
- **[FastAPI Documentation](https://fastapi.tiangolo.com/)** - Official FastAPI docs
- **[SQLAlchemy Documentation](https://docs.sqlalchemy.org/)** - Database ORM docs

## 🏆 Project Status

- ✅ **Core Features**: Authentication, user management, admin system
- ✅ **Production Ready**: Docker, monitoring, logging, security
- ✅ **Documentation**: Comprehensive API documentation
- 🔄 **In Progress**: Comprehensive test suite
- 📋 **Planned**: CI/CD pipeline, advanced monitoring

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **FastAPI** - For the amazing web framework
- **SQLAlchemy** - For the powerful ORM
- **Pydantic** - For data validation
- **Docker** - For containerization
- **PostgreSQL** - For the robust database

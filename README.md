# FastAPI Todo Application

A modern, production-ready FastAPI application with authentication and todo management features. This project follows clean architecture principles and best practices for Python web development.

## 🏗️ Project Structure

```
app/
├── main.py                  # Application entry point
├── core/                    # Core configuration and utilities
│   ├── config.py           # Application settings and configuration
│   └── security.py         # Security utilities (JWT, password hashing)
├── db/                      # Database layer
│   ├── base.py             # Database connection and session management
│   ├── models/             # SQLAlchemy ORM models
│   └── migrations/         # Database migration files
├── api/                     # API layer
│   ├── routes/             # API route handlers
│   │   ├── auth.py         # Authentication endpoints
│   │   └── todos.py        # Todo CRUD endpoints
│   └── deps.py             # Dependency injection functions
├── schemas/                 # Pydantic models for request/response validation
│   ├── auth.py             # Authentication schemas
│   └── todos.py            # Todo schemas
├── services/                # Business logic layer
│   ├── auth_service.py     # Authentication business logic
│   └── todo_service.py     # Todo management business logic
└── tests/                   # Test cases
    ├── test_auth.py        # Authentication tests
    └── test_todos.py       # Todo functionality tests
```

## 🚀 Features

- **Authentication System**: JWT-based authentication with user registration and login
- **Todo Management**: Full CRUD operations for todo items
- **Clean Architecture**: Separation of concerns with distinct layers
- **Data Validation**: Pydantic schemas for request/response validation
- **Database Integration**: SQLAlchemy ORM with PostgreSQL/SQLite support
- **Security**: Password hashing, JWT tokens, and secure endpoints
- **Testing**: Comprehensive test suite with pytest
- **Documentation**: Auto-generated API documentation with Swagger UI

## 🛠️ Technology Stack

- **Framework**: FastAPI
- **Database**: SQLAlchemy ORM (PostgreSQL/SQLite)
- **Authentication**: JWT tokens with passlib for password hashing
- **Validation**: Pydantic models
- **Testing**: pytest with test client
- **Documentation**: Automatic OpenAPI/Swagger documentation

## 📋 Prerequisites

- Python 3.8+
- pip or poetry for package management
- PostgreSQL (optional, SQLite works for development)

## 🔧 Installation & Setup

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd fast-api
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Environment configuration**
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

5. **Database setup**
   ```bash
   # Run database migrations
   alembic upgrade head
   ```

6. **Run the application**
   ```bash
   uvicorn app.main:app --reload
   ```

## 🌐 API Endpoints

### Authentication
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `GET /auth/me` - Get current user profile

### Todos
- `GET /todos/` - Get all todos for authenticated user
- `POST /todos/` - Create a new todo
- `GET /todos/{todo_id}` - Get specific todo
- `PUT /todos/{todo_id}` - Update todo
- `DELETE /todos/{todo_id}` - Delete todo

## 📚 API Documentation

Once the server is running, visit:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 🧪 Testing

Run the test suite:
```bash
pytest
```

Run with coverage:
```bash
pytest --cov=app
```

## 🔒 Environment Variables

Create a `.env` file with the following variables:

```env
# Database
DATABASE_URL=sqlite:///./test.db
# For PostgreSQL: postgresql://user:password@localhost/dbname

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Application
DEBUG=True
```

## 🏃‍♂️ Quick Start Guide

1. **Start the server**: `uvicorn app.main:app --reload`
2. **Open browser**: Navigate to http://localhost:8000/docs
3. **Register a user**: Use the `/auth/register` endpoint
4. **Login**: Use the `/auth/login` endpoint to get a token
5. **Authorize**: Click "Authorize" in Swagger UI and enter your token
6. **Create todos**: Use the `/todos/` endpoints to manage your todos

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

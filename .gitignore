# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
env/
venv/
ENV/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Pyre type checker
.pyre/

# mypy type checker
.mypy_cache/
.dmypy.json
dmypy.json

# PyCharm / IntelliJ
.idea/
*.iml

# VS Code
.vscode/

# Jupyter Notebook checkpoints
.ipynb_checkpoints

# Logs
*.log

# OS files
.DS_Store
Thumbs.db

# FastAPI / SQLAlchemy / Alembic
migrations/
*.db
*.sqlite3

# dotenv / environment variables
.env
.env.*

# node modules (if using frontend in same repo)
node_modules/

from sqlalchemy.orm import Session
from typing import Optional, List
import logging

from app.db.models.admin import Admin
from app.schemas.admin import AdminCreate, AdminUpdate, AdminResponse
from app.services.base import BaseService
from app.core.exceptions import (
    raise_duplicate,
    raise_validation_error,
    raise_not_found,
)
from app.core.security import get_password_hash

logger = logging.getLogger(__name__)


class AdminService(BaseService[Admin, AdminCreate, AdminUpdate]):
    """Service for managing admin operations with comprehensive exception handling."""

    def __init__(self, db: Session):
        super().__init__(Admin, db)

    def create_admin(self, admin_data: AdminCreate) -> Admin:
        """
        Create a new admin with validation and error handling.

        Args:
            admin_data: Admin creation data

        Returns:
            Created admin object

        Raises:
            ValidationError: If admin data is invalid
            DuplicateError: If email already exists
            DatabaseError: If database operation fails
        """
        # Validate email uniqueness
        existing_admin = self.get_by_email(admin_data.email)
        if existing_admin:
            raise_duplicate("Admin", "email", admin_data.email)

        # Validate password strength (basic example)
        if len(admin_data.password) < 8:
            raise_validation_error("Password must be at least 8 characters long")

        # Hash password and create admin data
        admin_dict = admin_data.model_dump(exclude={"password"})
        admin_dict["password"] = get_password_hash(admin_data.password)

        # Create admin object manually to handle password hashing
        from app.db.models.admin import Admin

        db_admin = Admin(**admin_dict)

        try:
            self.db.add(db_admin)
            self.db.commit()
            self.db.refresh(db_admin)

            logger.info(f"Admin created successfully: {db_admin.email}")
            return db_admin

        except Exception as e:
            self.db.rollback()
            self._handle_database_error(e, "create_admin")

    def get_by_email(self, email: str) -> Optional[Admin]:
        """Get admin by email address."""
        try:
            if not email or not email.strip():
                raise_validation_error("Email cannot be empty")

            logger.debug(f"Getting admin by email: {email}")
            return (
                self.db.query(Admin)
                .filter(Admin.email == email.lower().strip())
                .first()
            )
        except Exception as e:
            self._handle_database_error(e, "get_by_email")

    def update_admin(self, admin_id: int, admin_data: AdminUpdate) -> Admin:
        """
        Update an existing admin with validation.

        Args:
            admin_id: ID of admin to update
            admin_data: Updated admin data

        Returns:
            Updated admin object
        """
        # Check if email is being changed and if it's unique
        existing_admin = self.get_or_404(admin_id)
        if admin_data.email != existing_admin.email:
            email_exists = self.get_by_email(admin_data.email)
            if email_exists:
                raise_duplicate("Admin", "email", admin_data.email)

        return self.update(admin_id, admin_data)

    def get_all_admins(self, skip: int = 0, limit: int = 100) -> List[Admin]:
        """Get all admins with pagination."""
        return self.get_all(skip=skip, limit=limit)

    def delete_admin(self, admin_id: int) -> bool:
        """Delete an admin by ID."""
        return self.delete(admin_id)

    def admin_exists(self, admin_id: int) -> bool:
        """Check if admin exists by ID."""
        return self.exists(admin_id)


# Legacy function for backward compatibility
def create_admin(db: Session, admin: AdminCreate) -> Admin:
    """
    Legacy function for creating admin - use AdminService.create_admin instead.
    This function is kept for backward compatibility.
    """
    service = AdminService(db)
    return service.create_admin(admin)

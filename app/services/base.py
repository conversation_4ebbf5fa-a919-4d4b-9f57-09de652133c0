from typing import Generic, TypeVar, Type, Optional, List, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
import logging

from app.core.exceptions import (
    BaseAppException,
    DatabaseError,
    raise_not_found,
    raise_duplicate,
    raise_validation_error,
)

ModelType = TypeVar("ModelType")
CreateSchemaType = TypeVar("CreateSchemaType")
UpdateSchemaType = TypeVar("UpdateSchemaType")

logger = logging.getLogger(__name__)


class BaseService(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """
    Base service class with common CRUD operations and comprehensive exception handling.

    This class provides:
    - Standard CRUD operations (Create, Read, Update, Delete)
    - Consistent exception handling across all services
    - Database error handling and transaction management
    - Logging for debugging and monitoring
    """

    def __init__(self, model: Type[ModelType], db: Session):
        self.model = model
        self.db = db
        self.model_name = model.__name__

    def _handle_database_error(self, error: Exception, operation: str) -> None:
        """Handle database errors and convert them to appropriate exceptions."""
        logger.error(
            f"Database error during {operation} for {self.model_name}: {str(error)}"
        )

        if isinstance(error, IntegrityError):
            # Handle unique constraint violations
            if (
                "UNIQUE constraint failed" in str(error)
                or "duplicate key" in str(error).lower()
            ):
                raise_duplicate(self.model_name, "field", "value")
            else:
                raise DatabaseError(
                    f"Integrity constraint violation during {operation}"
                )
        elif isinstance(error, SQLAlchemyError):
            raise DatabaseError(f"Database error during {operation}: {str(error)}")
        else:
            raise DatabaseError(f"Unexpected error during {operation}")

    def _validate_id(self, id: Any) -> int:
        """Validate and convert ID to integer."""
        if id is None:
            raise_validation_error("ID cannot be None")

        try:
            return int(id)
        except (ValueError, TypeError):
            raise_validation_error(f"Invalid ID format: {id}")

    def get(self, id: int) -> Optional[ModelType]:
        """Get a record by ID."""
        try:
            validated_id = self._validate_id(id)
            logger.debug(f"Getting {self.model_name} with ID: {validated_id}")
            return (
                self.db.query(self.model).filter(self.model.id == validated_id).first()
            )
        except BaseAppException:
            raise
        except Exception as e:
            self._handle_database_error(e, "get")

    def get_or_404(self, id: int) -> ModelType:
        """Get a record by ID or raise NotFoundError."""
        obj = self.get(id)
        if not obj:
            logger.warning(f"{self.model_name} with ID {id} not found")
            raise_not_found(self.model_name, id)
        return obj

    def get_all(self, skip: int = 0, limit: int = 100) -> List[ModelType]:
        """Get all records with pagination."""
        try:
            if skip < 0:
                raise_validation_error("Skip value cannot be negative")
            if limit <= 0 or limit > 1000:
                raise_validation_error("Limit must be between 1 and 1000")

            logger.debug(
                f"Getting {self.model_name} records with skip={skip}, limit={limit}"
            )
            return self.db.query(self.model).offset(skip).limit(limit).all()
        except BaseAppException:
            raise
        except Exception as e:
            self._handle_database_error(e, "get_all")

    def create(self, obj_data: CreateSchemaType) -> ModelType:
        """Create a new record."""
        try:
            logger.info(f"Creating new {self.model_name}")

            # Convert Pydantic model to dict
            if hasattr(obj_data, "model_dump"):
                data_dict = obj_data.model_dump(exclude_unset=True)
            elif hasattr(obj_data, "dict"):
                data_dict = obj_data.dict(exclude_unset=True)
            else:
                raise_validation_error("Invalid data format for creation")

            # Create database object
            db_obj = self.model(**data_dict)

            # Save to database
            self.db.add(db_obj)
            self.db.commit()
            self.db.refresh(db_obj)

            logger.info(f"{self.model_name} created successfully with ID: {db_obj.id}")
            return db_obj

        except BaseAppException:
            self.db.rollback()
            raise
        except Exception as e:
            self.db.rollback()
            self._handle_database_error(e, "create")

    def update(self, id: int, obj_data: UpdateSchemaType) -> ModelType:
        """Update an existing record."""
        try:
            logger.info(f"Updating {self.model_name} with ID: {id}")

            # Get existing object
            db_obj = self.get_or_404(id)

            # Convert Pydantic model to dict
            if hasattr(obj_data, "model_dump"):
                data_dict = obj_data.model_dump(exclude_unset=True)
            elif hasattr(obj_data, "dict"):
                data_dict = obj_data.dict(exclude_unset=True)
            else:
                raise_validation_error("Invalid data format for update")

            # Update fields
            for field, value in data_dict.items():
                if hasattr(db_obj, field):
                    setattr(db_obj, field, value)

            # Save changes
            self.db.commit()
            self.db.refresh(db_obj)

            logger.info(f"{self.model_name} with ID {id} updated successfully")
            return db_obj

        except BaseAppException:
            self.db.rollback()
            raise
        except Exception as e:
            self.db.rollback()
            self._handle_database_error(e, "update")

    def delete(self, id: int) -> bool:
        """Delete a record by ID."""
        try:
            logger.info(f"Deleting {self.model_name} with ID: {id}")

            # Get existing object
            db_obj = self.get_or_404(id)

            # Delete object
            self.db.delete(db_obj)
            self.db.commit()

            logger.info(f"{self.model_name} with ID {id} deleted successfully")
            return True

        except BaseAppException:
            self.db.rollback()
            raise
        except Exception as e:
            self.db.rollback()
            self._handle_database_error(e, "delete")

    def exists(self, id: int) -> bool:
        """Check if a record exists by ID."""
        try:
            validated_id = self._validate_id(id)
            return (
                self.db.query(self.model).filter(self.model.id == validated_id).first()
                is not None
            )
        except BaseAppException:
            raise
        except Exception as e:
            self._handle_database_error(e, "exists")

    def count(self) -> int:
        """Get total count of records."""
        try:
            return self.db.query(self.model).count()
        except Exception as e:
            self._handle_database_error(e, "count")

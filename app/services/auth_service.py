from sqlalchemy.orm import Session
from typing import Dict, Any
import logging
from datetime import timedelta

from app.db.models.user import User
from app.schemas.user import UserCreate, UserLogin, Token, TokenData
from app.services.user_service import UserService
from app.core.security import (
    create_access_token,
    create_refresh_token,
    verify_token,
    verify_refresh_token,
)
from app.core.config import settings
from app.core.exceptions import (
    AuthenticationError,
    raise_validation_error,
)

logger = logging.getLogger(__name__)


class AuthService:
    """Service for handling authentication operations."""

    def __init__(self, db: Session):
        self.db = db
        self.user_service = UserService(db)

    def register_user(self, user_data: UserCreate) -> Token:
        """
        Register a new user and return authentication token.

        Args:
            user_data: User registration data

        Returns:
            Token response with access token and user info
        """
        logger.info(f"Registering new user: {user_data.email}")

        # Create user
        user = self.user_service.create_user(user_data)

        # Generate tokens
        token_data = {
            "sub": str(user.id),
            "email": user.email,
            "username": user.username,
        }

        access_token = create_access_token(token_data)
        refresh_token = create_refresh_token(token_data)

        logger.info(f"User registered successfully: {user.email}")

        return Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=user,
        )

    def login_user(self, login_data: UserLogin) -> Token:
        """
        Authenticate user and return token.

        Args:
            login_data: Login credentials

        Returns:
            Token response with access token and user info
        """
        logger.info(f"User login attempt: {login_data.email_or_username}")

        # Authenticate user
        user = self.user_service.authenticate_user(login_data)

        # Generate tokens
        token_data = {
            "sub": str(user.id),
            "email": user.email,
            "username": user.username,
        }

        access_token = create_access_token(token_data)
        refresh_token = create_refresh_token(token_data)

        logger.info(f"User logged in successfully: {user.email}")

        return Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=user,
        )

    def refresh_access_token(self, refresh_token: str) -> Token:
        """
        Refresh access token using refresh token.

        Args:
            refresh_token: Valid refresh token

        Returns:
            New token response
        """
        logger.info("Refreshing access token")

        # Verify refresh token
        try:
            payload = verify_refresh_token(refresh_token)
            user_id = payload.get("sub")

            if not user_id:
                raise AuthenticationError("Invalid refresh token payload")

            # Get user
            user = self.user_service.get_or_404(int(user_id))

            # Check if user is still active
            if not user.is_active:
                raise AuthenticationError("User account is deactivated")

            # Generate new tokens
            token_data = {
                "sub": str(user.id),
                "email": user.email,
                "username": user.username,
            }

            new_access_token = create_access_token(token_data)
            new_refresh_token = create_refresh_token(token_data)

            logger.info(f"Access token refreshed for user: {user.email}")

            return Token(
                access_token=new_access_token,
                token_type="bearer",
                expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
                user=user,
            )

        except Exception as e:
            logger.warning(f"Token refresh failed: {str(e)}")
            raise AuthenticationError("Invalid or expired refresh token")

    def get_current_user_from_token(self, token: str) -> User:
        """
        Get current user from access token.

        Args:
            token: JWT access token

        Returns:
            Current user object
        """
        try:
            # Verify token
            payload = verify_token(token)
            user_id = payload.get("sub")

            if not user_id:
                raise AuthenticationError("Invalid token payload")

            # Get user
            user = self.user_service.get_or_404(int(user_id))

            # Check if user is still active
            if not user.is_active:
                raise AuthenticationError("User account is deactivated")

            return user

        except Exception as e:
            logger.warning(f"Token verification failed: {str(e)}")
            raise AuthenticationError("Invalid or expired token")

    def logout_user(self, token: str) -> Dict[str, str]:
        """
        Logout user (in a real app, you'd blacklist the token).

        Args:
            token: JWT access token

        Returns:
            Success message
        """
        try:
            # Verify token to ensure it's valid
            payload = verify_token(token)
            user_id = payload.get("sub")

            if user_id:
                logger.info(f"User logged out: {user_id}")

            # In a production app, you would:
            # 1. Add token to blacklist/redis
            # 2. Store token expiry time
            # 3. Check blacklist in token verification

            return {"message": "Successfully logged out"}

        except Exception as e:
            logger.warning(f"Logout attempt with invalid token: {str(e)}")
            # Even if token is invalid, we can still return success
            # as the goal (user being logged out) is achieved
            return {"message": "Successfully logged out"}

    def validate_token_data(self, token: str) -> TokenData:
        """
        Validate token and return token data.

        Args:
            token: JWT access token

        Returns:
            Token data object
        """
        try:
            payload = verify_token(token)

            return TokenData(
                user_id=int(payload.get("sub", 0)), email=payload.get("email")
            )

        except Exception as e:
            logger.warning(f"Token validation failed: {str(e)}")
            raise AuthenticationError("Invalid token")

    def change_user_password(
        self, user_id: int, current_password: str, new_password: str
    ) -> bool:
        """
        Change user password (convenience method).

        Args:
            user_id: User ID
            current_password: Current password
            new_password: New password

        Returns:
            True if password changed successfully
        """
        from app.schemas.user import UserChangePassword

        password_data = UserChangePassword(
            current_password=current_password,
            new_password=new_password,
            confirm_new_password=new_password,
        )

        return self.user_service.change_password(user_id, password_data)

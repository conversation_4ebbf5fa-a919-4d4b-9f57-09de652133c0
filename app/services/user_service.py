from sqlalchemy.orm import Session
from typing import Optional, List
import logging

from app.db.models.user import User
from app.schemas.user import UserCreate, UserUpdate, UserLogin, UserChangePassword
from app.services.base import BaseService
from app.core.security import (
    get_password_hash,
    verify_password,
    create_access_token,
    create_refresh_token,
)
from app.core.exceptions import (
    raise_duplicate,
    raise_validation_error,
    raise_not_found,
    raise_business_logic_error,
    AuthenticationError,
)

logger = logging.getLogger(__name__)


class UserService(BaseService[User, UserCreate, UserUpdate]):
    """Service for managing user operations with comprehensive authentication."""

    def __init__(self, db: Session):
        super().__init__(User, db)

    def create_user(self, user_data: UserCreate) -> User:
        """
        Create a new user with validation and password hashing.
        
        Args:
            user_data: User creation data
            
        Returns:
            Created user object
            
        Raises:
            ValidationError: If user data is invalid
            DuplicateError: If email or username already exists
            DatabaseError: If database operation fails
        """
        logger.info(f"Creating new user with email: {user_data.email}")
        
        # Check for existing email
        existing_email = self.get_by_email(user_data.email)
        if existing_email:
            raise_duplicate("User", "email", user_data.email)
        
        # Check for existing username
        existing_username = self.get_by_username(user_data.username)
        if existing_username:
            raise_duplicate("User", "username", user_data.username)
        
        # Hash password
        hashed_password = get_password_hash(user_data.password)
        
        # Create user data dict without password confirmation
        user_dict = user_data.model_dump(exclude={"password", "confirm_password"})
        user_dict["hashed_password"] = hashed_password
        
        # Create user object
        db_user = User(**user_dict)
        
        try:
            self.db.add(db_user)
            self.db.commit()
            self.db.refresh(db_user)
            
            logger.info(f"User created successfully: {db_user.email}")
            return db_user
            
        except Exception as e:
            self.db.rollback()
            self._handle_database_error(e, "create_user")

    def authenticate_user(self, login_data: UserLogin) -> User:
        """
        Authenticate user with email/username and password.
        
        Args:
            login_data: Login credentials
            
        Returns:
            Authenticated user object
            
        Raises:
            AuthenticationError: If authentication fails
        """
        logger.info(f"Authenticating user: {login_data.email_or_username}")
        
        # Try to find user by email or username
        user = self.get_by_email_or_username(login_data.email_or_username)
        
        if not user:
            logger.warning(f"User not found: {login_data.email_or_username}")
            raise AuthenticationError("Invalid credentials")
        
        # Check if user is active
        if not user.is_active:
            logger.warning(f"Inactive user attempted login: {user.email}")
            raise AuthenticationError("Account is deactivated")
        
        # Verify password
        if not verify_password(login_data.password, user.hashed_password):
            logger.warning(f"Invalid password for user: {user.email}")
            raise AuthenticationError("Invalid credentials")
        
        logger.info(f"User authenticated successfully: {user.email}")
        return user

    def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email address."""
        if not email or not email.strip():
            raise_validation_error("Email cannot be empty")
        
        try:
            return (
                self.db.query(User)
                .filter(User.email == email.lower().strip())
                .first()
            )
        except Exception as e:
            self._handle_database_error(e, "get_by_email")

    def get_by_username(self, username: str) -> Optional[User]:
        """Get user by username."""
        if not username or not username.strip():
            raise_validation_error("Username cannot be empty")
        
        try:
            return (
                self.db.query(User)
                .filter(User.username == username.lower().strip())
                .first()
            )
        except Exception as e:
            self._handle_database_error(e, "get_by_username")

    def get_by_email_or_username(self, identifier: str) -> Optional[User]:
        """Get user by email or username."""
        if not identifier or not identifier.strip():
            raise_validation_error("Email or username cannot be empty")
        
        identifier = identifier.lower().strip()
        
        try:
            return (
                self.db.query(User)
                .filter(
                    (User.email == identifier) | (User.username == identifier)
                )
                .first()
            )
        except Exception as e:
            self._handle_database_error(e, "get_by_email_or_username")

    def update_user(self, user_id: int, user_data: UserUpdate) -> User:
        """
        Update user information.
        
        Args:
            user_id: ID of user to update
            user_data: Updated user data
            
        Returns:
            Updated user object
        """
        logger.info(f"Updating user: {user_id}")
        
        # Get existing user
        user = self.get_or_404(user_id)
        
        # Use base service update
        return self.update(user_id, user_data)

    def change_password(self, user_id: int, password_data: UserChangePassword) -> bool:
        """
        Change user password.
        
        Args:
            user_id: ID of user
            password_data: Password change data
            
        Returns:
            True if password changed successfully
        """
        logger.info(f"Changing password for user: {user_id}")
        
        # Get user
        user = self.get_or_404(user_id)
        
        # Verify current password
        if not verify_password(password_data.current_password, user.hashed_password):
            raise AuthenticationError("Current password is incorrect")
        
        # Hash new password
        new_hashed_password = get_password_hash(password_data.new_password)
        
        try:
            user.hashed_password = new_hashed_password
            self.db.commit()
            
            logger.info(f"Password changed successfully for user: {user.email}")
            return True
            
        except Exception as e:
            self.db.rollback()
            self._handle_database_error(e, "change_password")

    def deactivate_user(self, user_id: int) -> User:
        """Deactivate user account."""
        logger.info(f"Deactivating user: {user_id}")
        
        user = self.get_or_404(user_id)
        
        try:
            user.is_active = False
            self.db.commit()
            self.db.refresh(user)
            
            logger.info(f"User deactivated: {user.email}")
            return user
            
        except Exception as e:
            self.db.rollback()
            self._handle_database_error(e, "deactivate_user")

    def activate_user(self, user_id: int) -> User:
        """Activate user account."""
        logger.info(f"Activating user: {user_id}")
        
        user = self.get_or_404(user_id)
        
        try:
            user.is_active = True
            self.db.commit()
            self.db.refresh(user)
            
            logger.info(f"User activated: {user.email}")
            return user
            
        except Exception as e:
            self.db.rollback()
            self._handle_database_error(e, "activate_user")

    def verify_user(self, user_id: int) -> User:
        """Mark user as verified."""
        logger.info(f"Verifying user: {user_id}")
        
        user = self.get_or_404(user_id)
        
        try:
            user.is_verified = True
            self.db.commit()
            self.db.refresh(user)
            
            logger.info(f"User verified: {user.email}")
            return user
            
        except Exception as e:
            self.db.rollback()
            self._handle_database_error(e, "verify_user")

    def get_active_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """Get all active users."""
        try:
            return (
                self.db.query(User)
                .filter(User.is_active == True)
                .offset(skip)
                .limit(limit)
                .all()
            )
        except Exception as e:
            self._handle_database_error(e, "get_active_users")

    def search_users(self, query: str, limit: int = 10) -> List[User]:
        """Search users by name, email, or username."""
        if not query or len(query.strip()) < 2:
            raise_validation_error("Search query must be at least 2 characters")
        
        if limit <= 0 or limit > 100:
            raise_validation_error("Limit must be between 1 and 100")
        
        try:
            search_pattern = f"%{query.strip().lower()}%"
            
            return (
                self.db.query(User)
                .filter(
                    User.is_active == True,
                    (
                        User.first_name.ilike(search_pattern) |
                        User.last_name.ilike(search_pattern) |
                        User.email.ilike(search_pattern) |
                        User.username.ilike(search_pattern)
                    )
                )
                .limit(limit)
                .all()
            )
        except Exception as e:
            self._handle_database_error(e, "search_users")

from sqlalchemy import Column, String, Boolean, Index
from app.db.models.base import BaseModel


class Admin(BaseModel):
    """Admin model for administrative users."""

    __tablename__ = "admins"

    first_name = Column(String(50), nullable=False)
    last_name = Column(String(50), nullable=False)
    email = Column(String(254), unique=True, index=True, nullable=False)
    password = Column(
        String(128), nullable=False
    )  # This should be hashed_password in production
    is_active = Column(Boolean, default=True, nullable=False, index=True)

    def __repr__(self):
        return f"<Admin(id={self.id}, email='{self.email}')>"

    @property
    def full_name(self) -> str:
        """Get admin's full name."""
        return f"{self.first_name} {self.last_name}"


# Additional indexes for performance
Index("idx_admin_active", Admin.is_active)
Index("idx_admin_created_at", Admin.created_at)
Index("idx_admin_updated_at", Admin.updated_at)

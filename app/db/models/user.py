from sqlalchemy import Column, String, Boolean, Text, Index
from sqlalchemy.orm import relationship
from app.db.models.base import BaseModel


class User(BaseModel):
    """User model for authentication and user management."""

    __tablename__ = "users"

    # Basic user information
    email = Column(String(254), unique=True, index=True, nullable=False)
    username = Column(String(50), unique=True, index=True, nullable=False)
    first_name = Column(String(50), nullable=False)
    last_name = Column(String(50), nullable=False)

    # Authentication
    hashed_password = Column(String(128), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_verified = Column(Boolean, default=False, nullable=False, index=True)

    # Optional fields
    bio = Column(Text, nullable=True)
    avatar_url = Column(String(500), nullable=True)

    def __repr__(self):
        return f"<User(id={self.id}, email='{self.email}', username='{self.username}')>"

    @property
    def full_name(self) -> str:
        """Get user's full name."""
        return f"{self.first_name} {self.last_name}"

    @property
    def is_authenticated(self) -> bool:
        """Check if user is authenticated (active and verified)."""
        return self.is_active and self.is_verified


# Additional indexes for performance
Index("idx_user_active_verified", User.is_active, User.is_verified)
Index("idx_user_created_at", User.created_at)
Index("idx_user_updated_at", User.updated_at)

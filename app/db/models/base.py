from app.db.base import Base
from sqlalchemy import Column, DateTime, Integer
from app.core.utils import utc_now


class BaseModel(Base):
    """Base model with common fields."""

    __abstract__ = True

    id = Column(Integer, primary_key=True, index=True)

    # Audit fields
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)
    deleted_at = Column(DateTime, nullable=True)  # For soft delete

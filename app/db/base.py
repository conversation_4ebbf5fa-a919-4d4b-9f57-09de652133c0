"""
Database configuration and session management.
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.orm import declarative_base
from typing import Generator
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

# Database engine configuration
engine_kwargs = {
    "echo": settings.database_echo,
    "future": True,
    "pool_pre_ping": True,  # Verify connections before use
    "pool_recycle": 3600,  # Recycle connections every hour
}

# Add connection pooling for production
if settings.is_production:
    engine_kwargs.update(
        {
            "pool_size": 20,
            "max_overflow": 30,
            "pool_timeout": 30,
        }
    )

engine = create_engine(settings.DATABASE_URL, **engine_kwargs)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db() -> Generator[Session, None, None]:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


Base = declarative_base()

from pydantic import BaseModel
from datetime import datetime
from typing import Optional


class BaseSchema(BaseModel):
    """Base schema with common fields."""

    class Config:
        from_attributes = True
        validate_assignment = True
        arbitrary_types_allowed = True
        str_strip_whitespace = True
        json_encoders = {datetime: lambda v: v.isoformat()}


class TimeStampMixin(BaseSchema):
    """Mixin to add timestamp fields."""

    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

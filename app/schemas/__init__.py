"""
Pydantic Schemas

This package contains all Pydantic models used for request/response validation
and serialization throughout the application.
"""

# Import base schemas
from app.schemas.base import BaseSchema, TimeStampMixin

# Import user schemas
from app.schemas.user import (
    UserBase,
    UserCreate,
    UserUpdate,
    UserResponse,
    UserLogin,
    Token,
)

# Import admin schemas
from app.schemas.admin import (
    AdminCreate,
    AdminUpdate,
    AdminResponse,
)

__all__ = [
    # Base schemas
    "BaseSchema",
    "TimeStampMixin",
    # User schemas
    "UserBase",
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "UserLogin",
    "Token",
    # Admin schemas
    "AdminCreate",
    "AdminUpdate",
    "AdminResponse",
]

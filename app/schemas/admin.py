from pydantic import EmailStr, constr
from typing import Annotated, Optional
from app.schemas.base import BaseSchema, TimeStampMixin


class AdminCreate(BaseSchema):
    first_name: Annotated[str, constr(min_length=2, max_length=50)]
    last_name: Annotated[str, constr(min_length=2, max_length=50)]
    email: EmailStr
    password: Annotated[str, constr(min_length=8, max_length=128)]


class AdminUpdate(BaseSchema):
    first_name: Optional[Annotated[str, constr(min_length=2, max_length=50)]] = None
    last_name: Optional[Annotated[str, constr(min_length=2, max_length=50)]] = None
    email: Optional[EmailStr] = None
    password: Optional[Annotated[str, constr(min_length=8, max_length=128)]] = None


class AdminResponse(TimeStampMixin):
    id: int
    first_name: str
    last_name: str
    email: EmailStr

    class Config:
        from_attributes = True

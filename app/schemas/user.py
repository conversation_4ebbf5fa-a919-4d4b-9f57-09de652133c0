from pydantic import <PERSON><PERSON><PERSON><PERSON>, <PERSON>, validator
from typing import Annotated, Optional
from app.schemas.base import BaseSchema, TimeStampMixin


class UserBase(BaseSchema):
    """Base user schema with common fields."""

    email: EmailStr
    username: Annotated[
        str, Field(min_length=3, max_length=50, pattern=r"^[a-zA-Z0-9_-]+$")
    ]
    first_name: Annotated[str, Field(min_length=2, max_length=50)]
    last_name: Annotated[str, Field(min_length=2, max_length=50)]
    bio: Optional[str] = Field(None, max_length=500)
    avatar_url: Optional[str] = Field(None, max_length=500)


class UserCreate(UserBase):
    """Schema for user creation."""

    password: Annotated[str, Field(min_length=8, max_length=128)]
    confirm_password: str

    @validator("confirm_password")
    def passwords_match(cls, v, values, **kwargs):
        if "password" in values and v != values["password"]:
            raise ValueError("Passwords do not match")
        return v

    @validator("password")
    def validate_password_strength(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")

        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in v)

        if not (has_upper and has_lower and has_digit):
            raise ValueError(
                "Password must contain at least one uppercase letter, one lowercase letter, and one digit"
            )

        return v


class UserUpdate(BaseSchema):
    """Schema for user updates."""

    first_name: Optional[Annotated[str, Field(min_length=2, max_length=50)]] = None
    last_name: Optional[Annotated[str, Field(min_length=2, max_length=50)]] = None
    bio: Optional[str] = Field(None, max_length=500)
    avatar_url: Optional[str] = Field(None, max_length=500)


class UserResponse(UserBase, TimeStampMixin):
    """Schema for user responses."""

    id: int
    is_active: bool
    is_verified: bool
    full_name: str

    class Config:
        from_attributes = True


class UserLogin(BaseSchema):
    """Schema for user login."""

    email_or_username: str
    password: str


class UserChangePassword(BaseSchema):
    """Schema for password change."""

    current_password: str
    new_password: Annotated[str, Field(min_length=8, max_length=128)]
    confirm_new_password: str

    @validator("confirm_new_password")
    def passwords_match(cls, v, values, **kwargs):
        if "new_password" in values and v != values["new_password"]:
            raise ValueError("New passwords do not match")
        return v

    @validator("new_password")
    def validate_password_strength(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")

        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)

        if not (has_upper and has_lower and has_digit):
            raise ValueError(
                "Password must contain at least one uppercase letter, one lowercase letter, and one digit"
            )

        return v


class Token(BaseSchema):
    """Schema for JWT token response."""

    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserResponse


class TokenData(BaseSchema):
    """Schema for token data."""

    user_id: Optional[int] = None
    email: Optional[str] = None

"""
API versioning utilities and configuration.
"""

from typing import Dict, Any, List
from fastapi import APIRouter, Request
from fastapi.responses import J<PERSON>NResponse
from app.core.config import settings


class APIVersion:
    """API version information."""

    def __init__(self, version: str, status: str = "stable", deprecated: bool = False):
        self.version = version
        self.status = status  # stable, beta, alpha, deprecated
        self.deprecated = deprecated

    def to_dict(self) -> Dict[str, Any]:
        return {
            "version": self.version,
            "status": self.status,
            "deprecated": self.deprecated,
        }


# Define available API versions
API_VERSIONS = {
    "v1": APIVersion("1.0.0", "stable", False),
    # Future versions can be added here
    # "v2": APIVersion("2.0.0", "beta", False),
}

CURRENT_VERSION = "v1"
SUPPORTED_VERSIONS = list(API_VERSIONS.keys())


def get_api_version_info() -> Dict[str, Any]:
    """Get information about all API versions."""
    return {
        "current_version": CURRENT_VERSION,
        "supported_versions": SUPPORTED_VERSIONS,
        "versions": {version: info.to_dict() for version, info in API_VERSIONS.items()},
        "deprecation_policy": {
            "notice_period_months": 6,
            "support_period_months": 12,
            "documentation": "/docs",
        },
    }


def create_version_router(version: str) -> APIRouter:
    """Create a router for a specific API version."""
    if version not in API_VERSIONS:
        raise ValueError(f"Unsupported API version: {version}")

    version_info = API_VERSIONS[version]

    router = APIRouter()

    @router.get(f"/{version}")
    def version_info_endpoint():
        """Get information about this API version."""
        return {
            "version": version_info.version,
            "status": version_info.status,
            "deprecated": version_info.deprecated,
            "endpoints": {
                "authentication": f"/api/{version}/auth",
                "users": f"/api/{version}/users",
                "admin": f"/api/{version}/admins",
                "health": f"/api/{version}/health",
            },
            "documentation": f"/docs",
            "openapi": f"/openapi.json",
        }

    return router


def add_version_headers(response: JSONResponse, version: str) -> JSONResponse:
    """Add version headers to response."""
    if version in API_VERSIONS:
        version_info = API_VERSIONS[version]
        response.headers["X-API-Version"] = version_info.version
        response.headers["X-API-Status"] = version_info.status

        if version_info.deprecated:
            response.headers["X-API-Deprecated"] = "true"
            response.headers["Warning"] = f'299 - "API version {version} is deprecated"'

    return response


class VersionMiddleware:
    """Middleware to handle API versioning."""

    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            request = Request(scope, receive)

            # Extract version from path
            path_parts = request.url.path.strip("/").split("/")
            version = None

            if len(path_parts) >= 2 and path_parts[0] == "api":
                potential_version = path_parts[1]
                if potential_version in SUPPORTED_VERSIONS:
                    version = potential_version

            # Add version info to request state
            if not hasattr(request, "state"):
                request.state = type("State", (), {})()

            request.state.api_version = version
            request.state.api_version_info = (
                API_VERSIONS.get(version) if version else None
            )

        await self.app(scope, receive, send)


def get_version_from_request(request: Request) -> str:
    """Extract API version from request."""
    # Try to get from request state (set by middleware)
    if hasattr(request.state, "api_version") and request.state.api_version:
        return request.state.api_version

    # Try to get from path
    path_parts = request.url.path.strip("/").split("/")
    if len(path_parts) >= 2 and path_parts[0] == "api":
        potential_version = path_parts[1]
        if potential_version in SUPPORTED_VERSIONS:
            return potential_version

    # Try to get from header
    version_header = request.headers.get("X-API-Version")
    if version_header and version_header in SUPPORTED_VERSIONS:
        return version_header

    # Default to current version
    return CURRENT_VERSION


def validate_version_compatibility(
    requested_version: str, required_version: str = None
) -> bool:
    """Validate if requested version is compatible with required version."""
    if requested_version not in SUPPORTED_VERSIONS:
        return False

    if required_version and requested_version != required_version:
        return False

    # Check if version is deprecated
    version_info = API_VERSIONS.get(requested_version)
    if version_info and version_info.deprecated:
        # You might want to log a warning here
        pass

    return True


def get_version_migration_info(from_version: str, to_version: str) -> Dict[str, Any]:
    """Get migration information between API versions."""
    if from_version not in SUPPORTED_VERSIONS or to_version not in SUPPORTED_VERSIONS:
        return {"error": "Invalid version specified"}

    # This would contain actual migration information in a real application
    migration_info = {
        "from_version": from_version,
        "to_version": to_version,
        "breaking_changes": [],
        "new_features": [],
        "deprecated_features": [],
        "migration_guide": f"/docs/migration/{from_version}-to-{to_version}",
        "estimated_effort": "low",  # low, medium, high
    }

    # Example migration information (this would be version-specific)
    if from_version == "v1" and to_version == "v2":
        migration_info.update(
            {
                "breaking_changes": [
                    "Authentication endpoint moved from /auth to /authentication",
                    "User ID format changed from integer to UUID",
                ],
                "new_features": [
                    "Enhanced user profiles",
                    "Real-time notifications",
                    "Advanced search capabilities",
                ],
                "deprecated_features": [
                    "Legacy admin endpoints",
                    "Old pagination format",
                ],
                "estimated_effort": "medium",
            }
        )

    return migration_info

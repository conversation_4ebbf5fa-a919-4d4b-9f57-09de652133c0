"""
Authentication routes for user registration, login, and token management.
"""

from fastapi import APIRouter, Depends, status, Body
from sqlalchemy.orm import Session
from typing import Dict

from app.db.base import get_db
from app.schemas.user import (
    UserCreate,
    UserUpdate,
    UserLogin,
    UserResponse,
    UserChangePassword,
    Token,
)
from app.services.auth_service import AuthService
from app.services.user_service import UserService
from app.api.deps import (
    get_auth_service,
    get_user_service,
    get_current_user,
    get_current_active_user,
)
from app.db.models.user import User

router = APIRouter()


@router.post("/register", response_model=Token, status_code=status.HTTP_201_CREATED)
def register_user(
    user_data: UserCreate, auth_service: AuthService = Depends(get_auth_service)
):
    """
    Register a new user account.

    Creates a new user with the provided information and returns
    an authentication token for immediate login.
    """
    return auth_service.register_user(user_data)


@router.post("/login", response_model=Token)
def login_user(
    login_data: UserLogin, auth_service: AuthService = Depends(get_auth_service)
):
    """
    Authenticate user and return access token.

    Accepts either email or username along with password.
    Returns JWT token for accessing protected endpoints.
    """
    return auth_service.login_user(login_data)


@router.post("/refresh", response_model=Token)
def refresh_token(
    refresh_token: str = Body(..., embed=True),
    auth_service: AuthService = Depends(get_auth_service),
):
    """
    Refresh access token using refresh token.

    Provides a new access token when the current one expires.
    Refresh tokens have longer expiration times.
    """
    return auth_service.refresh_access_token(refresh_token)


@router.post("/logout")
def logout_user(
    current_user: User = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service),
):
    """
    Logout current user.

    In a production environment, this would blacklist the current token.
    For now, it simply validates the token and returns success.
    """
    # Get token from the dependency (we'd need to modify deps to get the actual token)
    # For now, we'll just return success
    return {"message": "Successfully logged out"}


@router.get("/me", response_model=UserResponse)
def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """
    Get current user information.

    Returns the profile information of the currently authenticated user.
    Requires a valid JWT token.
    """
    return current_user


@router.put("/me", response_model=UserResponse)
def update_current_user(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    user_service: UserService = Depends(get_user_service),
):
    """
    Update current user information.

    Allows users to update their profile information.
    Cannot update email or username through this endpoint.
    """
    return user_service.update_user(current_user.id, user_update)


@router.post("/change-password")
def change_password(
    password_data: UserChangePassword,
    current_user: User = Depends(get_current_active_user),
    auth_service: AuthService = Depends(get_auth_service),
):
    """
    Change user password.

    Requires current password for verification and new password.
    All active sessions will remain valid until tokens expire.
    """
    success = auth_service.change_user_password(
        current_user.id, password_data.current_password, password_data.new_password
    )

    if success:
        return {"message": "Password changed successfully"}
    else:
        return {"message": "Failed to change password"}


@router.post("/deactivate")
def deactivate_account(
    current_user: User = Depends(get_current_active_user),
    user_service: UserService = Depends(get_user_service),
):
    """
    Deactivate current user account.

    This will prevent the user from logging in until the account
    is reactivated by an administrator.
    """
    user_service.deactivate_user(current_user.id)
    return {"message": "Account deactivated successfully"}


@router.get("/verify-token")
def verify_token(current_user: User = Depends(get_current_user)):
    """
    Verify if the current token is valid.

    Returns user information if token is valid.
    Useful for client-side token validation.
    """
    return {
        "valid": True,
        "user_id": current_user.id,
        "email": current_user.email,
        "username": current_user.username,
        "is_active": current_user.is_active,
        "is_verified": current_user.is_verified,
    }

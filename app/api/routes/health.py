"""
Health check and monitoring endpoints.
"""

import time
import psutil
from datetime import datetime, timezone
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import text

from app.db.base import get_db, engine
from app.core.config import settings
from app.core.validation import (
    validate_all_configuration,
    check_security_recommendations,
)
from app.core.monitoring import metrics_collector, SystemMetrics
from app.api.versioning import get_api_version_info

router = APIRouter()


def get_system_info() -> Dict[str, Any]:
    """Get system information."""
    try:
        return {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage("/").percent,
            "boot_time": datetime.fromtimestamp(
                psutil.boot_time(), tz=timezone.utc
            ).isoformat(),
        }
    except Exception:
        return {"error": "Unable to retrieve system information"}


def check_database_health(db: Session) -> Dict[str, Any]:
    """Check database connectivity and health."""
    try:
        start_time = time.time()

        # Simple query to test connection
        result = db.execute(text("SELECT 1"))
        result.fetchone()

        response_time = time.time() - start_time

        return {
            "status": "healthy",
            "response_time_ms": round(response_time * 1000, 2),
            "connection_pool": {
                "size": engine.pool.size(),
                "checked_in": engine.pool.checkedin(),
                "checked_out": engine.pool.checkedout(),
            },
        }
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}


@router.get("/health")
def basic_health_check():
    """Basic health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "version": settings.VERSION,
        "environment": "development" if settings.DEBUG else "production",
    }


@router.get("/health/detailed")
def detailed_health_check(db: Session = Depends(get_db)):
    """Detailed health check with system and database information."""

    health_data = {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "version": settings.VERSION,
        "environment": "development" if settings.DEBUG else "production",
        "uptime": time.time(),  # This would be actual uptime in a real app
        "system": get_system_info(),
        "database": check_database_health(db),
        "configuration": {
            "debug": settings.DEBUG,
            "rate_limiting": settings.RATE_LIMIT_ENABLED,
            "cors_origins": len(settings.cors_origins_list),
        },
    }

    # Determine overall health status
    if health_data["database"]["status"] == "unhealthy":
        health_data["status"] = "unhealthy"

    # Check system resources
    system_info = health_data["system"]
    if isinstance(system_info, dict) and "cpu_percent" in system_info:
        if (
            system_info["cpu_percent"] > 90
            or system_info["memory_percent"] > 90
            or system_info["disk_percent"] > 90
        ):
            health_data["status"] = "degraded"

    return health_data


@router.get("/health/readiness")
def readiness_check(db: Session = Depends(get_db)):
    """Kubernetes-style readiness probe."""

    # Check database connectivity
    db_health = check_database_health(db)

    if db_health["status"] != "healthy":
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Service not ready - database unavailable",
        )

    return {
        "status": "ready",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "checks": {"database": "ok"},
    }


@router.get("/health/liveness")
def liveness_check():
    """Kubernetes-style liveness probe."""
    return {
        "status": "alive",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "version": settings.VERSION,
    }


@router.get("/health/configuration")
def configuration_check():
    """Check configuration validity and security."""

    # Run configuration validation
    validation_results = validate_all_configuration()

    # Get security recommendations
    security_recommendations = check_security_recommendations()

    response = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "configuration_valid": validation_results["valid"],
        "validation_results": validation_results,
        "security_recommendations": security_recommendations,
        "settings": {
            "project_name": settings.PROJECT_NAME,
            "version": settings.VERSION,
            "debug": settings.DEBUG,
            "environment": "development" if settings.DEBUG else "production",
            "database_type": (
                "postgresql" if "postgresql" in settings.DATABASE_URL else "other"
            ),
            "rate_limiting_enabled": settings.RATE_LIMIT_ENABLED,
            "cors_configured": len(settings.cors_origins_list) > 0,
        },
    }

    # Return appropriate status code
    if not validation_results["valid"]:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=response
        )

    return response


@router.get("/metrics")
def get_metrics(db: Session = Depends(get_db)):
    """Get application metrics (Prometheus-style)."""

    db_health = check_database_health(db)
    system_info = get_system_info()
    request_summary = metrics_collector.get_request_summary()

    # Record current system metrics
    if isinstance(system_info, dict) and "cpu_percent" in system_info:
        system_metrics = SystemMetrics(
            timestamp=datetime.now(timezone.utc),
            cpu_percent=system_info["cpu_percent"],
            memory_percent=system_info["memory_percent"],
            disk_percent=system_info["disk_percent"],
            active_connections=request_summary.get("active_requests", 0),
        )
        metrics_collector.record_system_metrics(system_metrics)

    metrics = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "application": {
            "version": settings.VERSION,
            "environment": "development" if settings.DEBUG else "production",
            "uptime_seconds": time.time(),  # This would be actual uptime
        },
        "database": {
            "status": 1 if db_health["status"] == "healthy" else 0,
            "response_time_ms": db_health.get("response_time_ms", 0),
            "connection_pool_size": db_health.get("connection_pool", {}).get("size", 0),
            "connection_pool_checked_out": db_health.get("connection_pool", {}).get(
                "checked_out", 0
            ),
        },
        "system": system_info if isinstance(system_info, dict) else {},
        "http": request_summary,
        "alerts": metrics_collector.get_alerts(),
    }

    return metrics


@router.get("/info")
def get_application_info():
    """Get general application information."""
    return {
        "name": settings.PROJECT_NAME,
        "version": settings.VERSION,
        "description": "A production-ready FastAPI application with authentication and user management",
        "environment": "development" if settings.DEBUG else "production",
        "features": [
            "JWT Authentication",
            "User Management",
            "Admin Panel",
            "Rate Limiting",
            "Request Logging",
            "Health Checks",
            "Security Headers",
            "CORS Support",
            "Database Migrations",
            "Exception Handling",
        ],
        "api": {
            "version": "v1",
            "docs_url": "/docs",
            "redoc_url": "/redoc",
            "openapi_url": "/openapi.json",
        },
        "contact": {
            "documentation": "/docs",
            "health": "/health",
            "metrics": "/metrics",
        },
    }


@router.get("/metrics/requests")
def get_request_metrics(minutes: int = 5):
    """Get detailed request metrics for the last N minutes."""
    return {
        "period_minutes": minutes,
        "summary": metrics_collector.get_request_summary(minutes),
        "timestamp": datetime.now(timezone.utc).isoformat(),
    }


@router.get("/metrics/endpoints")
def get_endpoint_metrics():
    """Get metrics for all endpoints."""
    endpoints = {}

    # Get metrics for all tracked endpoints
    for endpoint_key in metrics_collector.request_counts.keys():
        endpoint_metrics = metrics_collector.get_endpoint_metrics(endpoint_key)
        endpoints[endpoint_key] = endpoint_metrics

    return {"timestamp": datetime.now(timezone.utc).isoformat(), "endpoints": endpoints}


@router.get("/metrics/alerts")
def get_alerts():
    """Get current system alerts."""
    return {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "alerts": metrics_collector.get_alerts(),
    }


@router.get("/version")
def get_version_info():
    """Get API version information."""
    return get_api_version_info()

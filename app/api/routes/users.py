"""
User management routes for admin operations.
"""

from fastapi import APIRouter, Depends, status, Query
from typing import List, Optional

from app.db.base import get_db
from app.schemas.user import UserResponse, UserUpdate
from app.services.user_service import UserService
from app.api.deps import (
    get_user_service,
    get_current_active_user,
    require_admin_role,
    get_pagination_params,
    get_search_params,
)
from app.db.models.user import User

router = APIRouter()


@router.get("/", response_model=List[UserResponse])
def get_users(
    pagination: dict = Depends(get_pagination_params),
    current_user: User = Depends(require_admin_role),
    user_service: UserService = Depends(get_user_service),
):
    """
    Get all users with pagination (Admin only).

    Returns a paginated list of all users in the system.
    Requires admin privileges.
    """
    return user_service.get_all(**pagination)


@router.get("/active", response_model=List[UserResponse])
def get_active_users(
    pagination: dict = Depends(get_pagination_params),
    current_user: User = Depends(require_admin_role),
    user_service: UserService = Depends(get_user_service),
):
    """
    Get all active users with pagination (Admin only).

    Returns a paginated list of active users only.
    Requires admin privileges.
    """
    return user_service.get_active_users(**pagination)


@router.get("/search", response_model=List[UserResponse])
def search_users(
    search_params: dict = Depends(get_search_params),
    current_user: User = Depends(require_admin_role),
    user_service: UserService = Depends(get_user_service),
):
    """
    Search users by name, email, or username (Admin only).

    Searches across first name, last name, email, and username fields.
    Requires admin privileges.
    """
    if not search_params["query"]:
        return []

    return user_service.search_users(search_params["query"], search_params["limit"])


@router.get("/{user_id}", response_model=UserResponse)
def get_user(
    user_id: int,
    current_user: User = Depends(require_admin_role),
    user_service: UserService = Depends(get_user_service),
):
    """
    Get user by ID (Admin only).

    Returns detailed information about a specific user.
    Requires admin privileges.
    """
    return user_service.get_or_404(user_id)


@router.put("/{user_id}", response_model=UserResponse)
def update_user(
    user_id: int,
    user_update: UserUpdate,
    current_user: User = Depends(require_admin_role),
    user_service: UserService = Depends(get_user_service),
):
    """
    Update user information (Admin only).

    Allows admins to update any user's profile information.
    Cannot update passwords through this endpoint.
    """
    return user_service.update_user(user_id, user_update)


@router.post("/{user_id}/activate", response_model=UserResponse)
def activate_user(
    user_id: int,
    current_user: User = Depends(require_admin_role),
    user_service: UserService = Depends(get_user_service),
):
    """
    Activate user account (Admin only).

    Reactivates a deactivated user account.
    Requires admin privileges.
    """
    return user_service.activate_user(user_id)


@router.post("/{user_id}/deactivate", response_model=UserResponse)
def deactivate_user(
    user_id: int,
    current_user: User = Depends(require_admin_role),
    user_service: UserService = Depends(get_user_service),
):
    """
    Deactivate user account (Admin only).

    Prevents user from logging in until reactivated.
    Requires admin privileges.
    """
    return user_service.deactivate_user(user_id)


@router.post("/{user_id}/verify", response_model=UserResponse)
def verify_user(
    user_id: int,
    current_user: User = Depends(require_admin_role),
    user_service: UserService = Depends(get_user_service),
):
    """
    Verify user account (Admin only).

    Marks user account as verified.
    Requires admin privileges.
    """
    return user_service.verify_user(user_id)


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_user(
    user_id: int,
    current_user: User = Depends(require_admin_role),
    user_service: UserService = Depends(get_user_service),
):
    """
    Delete user account (Admin only).

    Permanently deletes a user account.
    This action cannot be undone.
    Requires admin privileges.
    """
    user_service.delete(user_id)
    return None


@router.get("/{user_id}/profile", response_model=UserResponse)
def get_user_profile(
    user_id: int,
    current_user: User = Depends(get_current_active_user),
    user_service: UserService = Depends(get_user_service),
):
    """
    Get user profile (Self or Admin).

    Users can view their own profile, admins can view any profile.
    """
    # Check if user is viewing their own profile or is admin
    if current_user.id != user_id and "admin" not in current_user.email.lower():
        from fastapi import HTTPException

        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Can only view your own profile",
        )

    return user_service.get_or_404(user_id)

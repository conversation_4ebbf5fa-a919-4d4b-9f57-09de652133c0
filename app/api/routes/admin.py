from fastapi import APIRouter, Depends, status
from sqlalchemy.orm import Session
from typing import List

from app.db.base import get_db
from app.schemas.admin import AdminCreate, AdminUpdate, AdminResponse
from app.services.admin_service import AdminService

router = APIRouter()


@router.post("/", response_model=AdminResponse, status_code=status.HTTP_201_CREATED)
def create_admin(admin: AdminCreate, db: Session = Depends(get_db)):
    """Create a new admin with comprehensive error handling."""
    service = AdminService(db)
    return service.create_admin(admin)


@router.get("/", response_model=List[AdminResponse])
def get_admins(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """Get all admins with pagination."""
    service = AdminService(db)
    return service.get_all_admins(skip=skip, limit=limit)


@router.get("/{admin_id}", response_model=AdminResponse)
def get_admin(admin_id: int, db: Session = Depends(get_db)):
    """Get a specific admin by ID."""
    service = AdminService(db)
    return service.get_or_404(admin_id)


@router.put("/{admin_id}", response_model=AdminResponse)
def update_admin(admin_id: int, admin: AdminUpdate, db: Session = Depends(get_db)):
    """Update an existing admin."""
    service = AdminService(db)
    return service.update_admin(admin_id, admin)


@router.delete("/{admin_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_admin(admin_id: int, db: Session = Depends(get_db)):
    """Delete an admin by ID."""
    service = AdminService(db)
    service.delete_admin(admin_id)
    return None

"""
Dependency injection for FastAPI routes.

This module provides reusable dependencies for authentication,
database sessions, and service injection.
"""

from typing import Generator, Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from app.db.base import get_db
from app.db.models.user import User
from app.services.auth_service import AuthService
from app.services.user_service import UserService
from app.services.admin_service import AdminService
from app.core.exceptions import AuthenticationError, to_http_exception

# Security scheme for JWT tokens
security = HTTPBearer()


def get_auth_service(db: Session = Depends(get_db)) -> AuthService:
    """Get authentication service instance."""
    return AuthService(db)


def get_user_service(db: Session = Depends(get_db)) -> UserService:
    """Get user service instance."""
    return UserService(db)


def get_admin_service(db: Session = Depends(get_db)) -> AdminService:
    """Get admin service instance."""
    return AdminService(db)


def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    auth_service: AuthService = Depends(get_auth_service)
) -> User:
    """
    Get current authenticated user from JWT token.
    
    Args:
        credentials: HTTP Bearer token credentials
        auth_service: Authentication service instance
        
    Returns:
        Current authenticated user
        
    Raises:
        HTTPException: If authentication fails
    """
    try:
        token = credentials.credentials
        user = auth_service.get_current_user_from_token(token)
        return user
    except AuthenticationError as e:
        http_exc = to_http_exception(e)
        raise HTTPException(
            status_code=http_exc.status_code,
            detail=http_exc.detail,
            headers={"WWW-Authenticate": "Bearer"}
        )


def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current active user (must be active and verified).
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Current active user
        
    Raises:
        HTTPException: If user is not active or verified
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User account is deactivated"
        )
    
    if not current_user.is_verified:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User account is not verified"
        )
    
    return current_user


def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    auth_service: AuthService = Depends(get_auth_service)
) -> Optional[User]:
    """
    Get current user if token is provided, otherwise return None.
    Useful for endpoints that work with or without authentication.
    
    Args:
        credentials: Optional HTTP Bearer token credentials
        auth_service: Authentication service instance
        
    Returns:
        Current user if authenticated, None otherwise
    """
    if not credentials:
        return None
    
    try:
        token = credentials.credentials
        user = auth_service.get_current_user_from_token(token)
        return user
    except AuthenticationError:
        return None


def require_admin_role(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """
    Require user to have admin role.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Current user if they have admin role
        
    Raises:
        HTTPException: If user doesn't have admin role
    """
    # For now, we'll check if user email contains 'admin'
    # In a real app, you'd have a proper role system
    if "admin" not in current_user.email.lower():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    
    return current_user


def get_pagination_params(
    skip: int = 0,
    limit: int = 100
) -> dict:
    """
    Get pagination parameters with validation.
    
    Args:
        skip: Number of records to skip
        limit: Maximum number of records to return
        
    Returns:
        Dictionary with validated pagination parameters
        
    Raises:
        HTTPException: If parameters are invalid
    """
    if skip < 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Skip parameter cannot be negative"
        )
    
    if limit <= 0 or limit > 1000:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Limit must be between 1 and 1000"
        )
    
    return {"skip": skip, "limit": limit}


def get_search_params(
    q: Optional[str] = None,
    limit: int = 10
) -> dict:
    """
    Get search parameters with validation.
    
    Args:
        q: Search query string
        limit: Maximum number of results
        
    Returns:
        Dictionary with validated search parameters
        
    Raises:
        HTTPException: If parameters are invalid
    """
    if q and len(q.strip()) < 2:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Search query must be at least 2 characters"
        )
    
    if limit <= 0 or limit > 100:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Search limit must be between 1 and 100"
        )
    
    return {"query": q.strip() if q else None, "limit": limit}

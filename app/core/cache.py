"""
Caching utilities and configuration.
"""

import json
import pickle
import hashlib
from typing import Any, Optional, Union, Dict, List
from datetime import datetime, timedelta
from functools import wraps
import logging

from app.core.config import settings

logger = logging.getLogger("app.cache")


class InMemoryCache:
    """Simple in-memory cache implementation."""
    
    def __init__(self, default_ttl: int = 300):  # 5 minutes default
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.default_ttl = default_ttl
        
    def _is_expired(self, item: Dict[str, Any]) -> bool:
        """Check if cache item is expired."""
        if 'expires_at' not in item:
            return False
        return datetime.now() > item['expires_at']
    
    def _cleanup_expired(self) -> None:
        """Remove expired items from cache."""
        expired_keys = [
            key for key, item in self.cache.items() 
            if self._is_expired(item)
        ]
        for key in expired_keys:
            del self.cache[key]
    
    def get(self, key: str) -> Optional[Any]:
        """Get item from cache."""
        self._cleanup_expired()
        
        if key not in self.cache:
            return None
            
        item = self.cache[key]
        if self._is_expired(item):
            del self.cache[key]
            return None
            
        return item['value']
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set item in cache."""
        ttl = ttl or self.default_ttl
        expires_at = datetime.now() + timedelta(seconds=ttl)
        
        self.cache[key] = {
            'value': value,
            'expires_at': expires_at,
            'created_at': datetime.now()
        }
    
    def delete(self, key: str) -> bool:
        """Delete item from cache."""
        if key in self.cache:
            del self.cache[key]
            return True
        return False
    
    def clear(self) -> None:
        """Clear all cache items."""
        self.cache.clear()
    
    def keys(self) -> List[str]:
        """Get all cache keys."""
        self._cleanup_expired()
        return list(self.cache.keys())
    
    def stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        self._cleanup_expired()
        return {
            'total_items': len(self.cache),
            'memory_usage_bytes': len(str(self.cache)),  # Rough estimate
            'oldest_item': min(
                (item['created_at'] for item in self.cache.values()),
                default=None
            ),
            'newest_item': max(
                (item['created_at'] for item in self.cache.values()),
                default=None
            )
        }


class RedisCache:
    """Redis cache implementation (placeholder for when Redis is available)."""
    
    def __init__(self, redis_url: str, default_ttl: int = 300):
        self.redis_url = redis_url
        self.default_ttl = default_ttl
        self.redis_client = None
        
        # Try to initialize Redis connection
        try:
            import redis
            self.redis_client = redis.from_url(redis_url)
            self.redis_client.ping()  # Test connection
            logger.info("Redis cache initialized successfully")
        except Exception as e:
            logger.warning(f"Redis not available, falling back to in-memory cache: {e}")
            self.redis_client = None
    
    def get(self, key: str) -> Optional[Any]:
        """Get item from Redis cache."""
        if not self.redis_client:
            return None
            
        try:
            data = self.redis_client.get(key)
            if data:
                return pickle.loads(data)
        except Exception as e:
            logger.error(f"Redis get error: {e}")
        
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set item in Redis cache."""
        if not self.redis_client:
            return
            
        try:
            ttl = ttl or self.default_ttl
            data = pickle.dumps(value)
            self.redis_client.setex(key, ttl, data)
        except Exception as e:
            logger.error(f"Redis set error: {e}")
    
    def delete(self, key: str) -> bool:
        """Delete item from Redis cache."""
        if not self.redis_client:
            return False
            
        try:
            return bool(self.redis_client.delete(key))
        except Exception as e:
            logger.error(f"Redis delete error: {e}")
            return False
    
    def clear(self) -> None:
        """Clear all cache items."""
        if not self.redis_client:
            return
            
        try:
            self.redis_client.flushdb()
        except Exception as e:
            logger.error(f"Redis clear error: {e}")


# Initialize cache based on configuration
if settings.REDIS_URL:
    cache = RedisCache(settings.REDIS_URL)
    # Fallback to in-memory if Redis fails
    if not cache.redis_client:
        cache = InMemoryCache()
else:
    cache = InMemoryCache()

logger.info(f"Cache initialized: {type(cache).__name__}")


def generate_cache_key(*args, **kwargs) -> str:
    """Generate a cache key from function arguments."""
    # Create a string representation of all arguments
    key_data = {
        'args': args,
        'kwargs': sorted(kwargs.items())
    }
    key_string = json.dumps(key_data, sort_keys=True, default=str)
    
    # Create a hash of the key string
    return hashlib.md5(key_string.encode()).hexdigest()


def cached(ttl: int = 300, key_prefix: str = ""):
    """Decorator to cache function results."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            func_name = f"{func.__module__}.{func.__name__}"
            arg_key = generate_cache_key(*args, **kwargs)
            cache_key = f"{key_prefix}{func_name}:{arg_key}"
            
            # Try to get from cache
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {func_name}")
                return cached_result
            
            # Execute function and cache result
            logger.debug(f"Cache miss for {func_name}")
            result = func(*args, **kwargs)
            cache.set(cache_key, result, ttl)
            
            return result
        
        # Add cache management methods to the function
        wrapper.cache_clear = lambda: cache.clear()
        wrapper.cache_info = lambda: cache.stats() if hasattr(cache, 'stats') else {}
        
        return wrapper
    return decorator


def cache_user_data(user_id: int, data: Dict[str, Any], ttl: int = 600) -> None:
    """Cache user-specific data."""
    cache_key = f"user:{user_id}:data"
    cache.set(cache_key, data, ttl)


def get_cached_user_data(user_id: int) -> Optional[Dict[str, Any]]:
    """Get cached user data."""
    cache_key = f"user:{user_id}:data"
    return cache.get(cache_key)


def invalidate_user_cache(user_id: int) -> None:
    """Invalidate all cache entries for a user."""
    cache_key = f"user:{user_id}:data"
    cache.delete(cache_key)


def cache_query_result(query_hash: str, result: Any, ttl: int = 300) -> None:
    """Cache database query results."""
    cache_key = f"query:{query_hash}"
    cache.set(cache_key, result, ttl)


def get_cached_query_result(query_hash: str) -> Optional[Any]:
    """Get cached query result."""
    cache_key = f"query:{query_hash}"
    return cache.get(cache_key)


def get_cache_stats() -> Dict[str, Any]:
    """Get comprehensive cache statistics."""
    stats = {
        "cache_type": type(cache).__name__,
        "timestamp": datetime.now().isoformat()
    }
    
    if hasattr(cache, 'stats'):
        stats.update(cache.stats())
    
    return stats


def clear_all_cache() -> None:
    """Clear all cache entries."""
    cache.clear()
    logger.info("All cache entries cleared")


# Example usage with the cached decorator
@cached(ttl=600, key_prefix="api:")
def get_user_profile(user_id: int) -> Dict[str, Any]:
    """Example cached function - get user profile."""
    # This would normally query the database
    return {
        "user_id": user_id,
        "profile": "cached_data",
        "timestamp": datetime.now().isoformat()
    }

"""
Configuration validation and environment checks.
"""

import os
import re
import secrets
from typing import List, Dict, Any
from app.core.config import settings
import logging

logger = logging.getLogger("app.validation")


class ConfigurationError(Exception):
    """Raised when configuration validation fails."""

    pass


def validate_secret_key() -> None:
    """Validate SECRET_KEY strength and security."""
    secret_key = settings.SECRET_KEY

    if not secret_key:
        raise ConfigurationError("SECRET_KEY is required")

    if len(secret_key) < 32:
        raise ConfigurationError("SECRET_KEY must be at least 32 characters long")

    # Check for common weak keys
    weak_keys = [
        "your-secret-key-here",
        "secret",
        "password",
        "123456",
        "changeme",
        "default",
    ]

    if secret_key.lower() in weak_keys:
        raise ConfigurationError("SECRET_KEY is too weak. Use a strong, random key.")

    # Check if it's the default development key
    if "your-super-secret-key" in secret_key and settings.is_production:
        raise ConfigurationError("Cannot use development SECRET_KEY in production")

    logger.info("SECRET_KEY validation passed")


def validate_database_url() -> None:
    """Validate database URL format and accessibility."""
    db_url = settings.DATABASE_URL

    if not db_url:
        raise ConfigurationError("DATABASE_URL is required")

    # Check URL format
    valid_schemes = ["postgresql", "postgresql+psycopg2", "sqlite", "mysql"]

    if not any(db_url.startswith(scheme + "://") for scheme in valid_schemes):
        raise ConfigurationError(
            f"DATABASE_URL must start with one of: {', '.join(valid_schemes)}"
        )

    # Warn about SQLite in production
    if db_url.startswith("sqlite") and settings.is_production:
        logger.warning("SQLite is not recommended for production use")

    logger.info("DATABASE_URL validation passed")


def validate_cors_origins() -> None:
    """Validate CORS origins configuration."""
    origins = settings.cors_origins_list

    if settings.is_production and "*" in origins:
        logger.warning(
            "CORS is configured to allow all origins (*) in production. "
            "Consider specifying exact origins for better security."
        )

    # Validate URL format for specific origins
    url_pattern = re.compile(
        r"^https?://"  # http:// or https://
        r"(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|"  # domain...
        r"localhost|"  # localhost...
        r"\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})"  # ...or ip
        r"(?::\d+)?"  # optional port
        r"(?:/?|[/?]\S+)$",
        re.IGNORECASE,
    )

    for origin in origins:
        if origin != "*" and not url_pattern.match(origin):
            logger.warning(f"Invalid CORS origin format: {origin}")

    logger.info("CORS origins validation passed")


def validate_production_settings() -> None:
    """Validate production-specific settings."""
    if not settings.is_production:
        return

    issues = []

    # Check DEBUG is disabled
    if settings.DEBUG:
        issues.append("DEBUG should be False in production")

    # Check database echo is disabled
    if settings.DATABASE_ECHO:
        issues.append("DATABASE_ECHO should be False in production")

    # Check rate limiting is enabled
    if not settings.RATE_LIMIT_ENABLED:
        issues.append("RATE_LIMIT_ENABLED should be True in production")

    # Check log level
    if settings.LOG_LEVEL == "DEBUG":
        issues.append("LOG_LEVEL should not be DEBUG in production")

    if issues:
        raise ConfigurationError(
            f"Production configuration issues: {'; '.join(issues)}"
        )

    logger.info("Production settings validation passed")


def validate_environment_variables() -> None:
    """Validate all required environment variables are set."""
    required_vars = [
        "SECRET_KEY",
        "DATABASE_URL",
    ]

    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        raise ConfigurationError(
            f"Missing required environment variables: {', '.join(missing_vars)}"
        )

    logger.info("Environment variables validation passed")


def generate_secure_secret_key() -> str:
    """Generate a secure secret key for development."""
    return secrets.token_urlsafe(32)


def validate_all_configuration() -> Dict[str, Any]:
    """Run all configuration validations and return status."""
    validation_results = {"valid": True, "errors": [], "warnings": [], "info": []}

    validations = [
        ("Environment Variables", validate_environment_variables),
        ("Secret Key", validate_secret_key),
        ("Database URL", validate_database_url),
        ("CORS Origins", validate_cors_origins),
        ("Production Settings", validate_production_settings),
    ]

    for name, validation_func in validations:
        try:
            validation_func()
            validation_results["info"].append(f"{name}: OK")
        except ConfigurationError as e:
            validation_results["valid"] = False
            validation_results["errors"].append(f"{name}: {str(e)}")
            logger.error(f"Configuration validation failed - {name}: {str(e)}")
        except Exception as e:
            validation_results["valid"] = False
            validation_results["errors"].append(f"{name}: Unexpected error - {str(e)}")
            logger.error(f"Unexpected validation error - {name}: {str(e)}")

    # Log summary
    if validation_results["valid"]:
        logger.info("All configuration validations passed")
    else:
        logger.error(
            f"Configuration validation failed with {len(validation_results['errors'])} errors"
        )

    return validation_results


def check_security_recommendations() -> List[str]:
    """Check security recommendations and return suggestions."""
    recommendations = []

    # Check if running in development mode
    if settings.DEBUG:
        recommendations.append("Disable DEBUG mode in production")

    # Check secret key strength
    if len(settings.SECRET_KEY) < 64:
        recommendations.append("Consider using a longer SECRET_KEY (64+ characters)")

    # Check database configuration
    if settings.DATABASE_URL.startswith("sqlite"):
        recommendations.append("Consider using PostgreSQL for production")

    # Check CORS configuration
    if "*" in settings.cors_origins_list:
        recommendations.append("Specify exact CORS origins instead of using '*'")

    # Check rate limiting
    if not settings.RATE_LIMIT_ENABLED:
        recommendations.append("Enable rate limiting for production")

    # Check HTTPS
    recommendations.append("Ensure HTTPS is enabled in production")
    recommendations.append("Consider implementing API versioning")
    recommendations.append("Set up monitoring and alerting")
    recommendations.append("Implement proper backup strategies")

    return recommendations


# Note: Call validate_all_configuration() explicitly in main.py to avoid circular imports

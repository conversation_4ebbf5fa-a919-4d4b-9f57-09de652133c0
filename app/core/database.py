"""
Database optimization utilities and query helpers.
"""

import time
import hashlib
from typing import Any, Dict, List, Optional, Type, TypeVar, Generic
from sqlalchemy.orm import Session, Query
from sqlalchemy.sql import text
from sqlalchemy import event, Engine
from sqlalchemy.pool import Pool
import logging

from app.core.cache import cache_query_result, get_cached_query_result
from app.core.config import settings

logger = logging.getLogger("app.database")

T = TypeVar('T')


class QueryOptimizer:
    """Database query optimization utilities."""
    
    @staticmethod
    def generate_query_hash(query: str, params: Dict[str, Any] = None) -> str:
        """Generate a hash for a query and its parameters."""
        query_data = {
            'query': query.strip(),
            'params': sorted((params or {}).items())
        }
        query_string = str(query_data)
        return hashlib.md5(query_string.encode()).hexdigest()
    
    @staticmethod
    def execute_cached_query(
        db: Session, 
        query: str, 
        params: Dict[str, Any] = None,
        ttl: int = 300
    ) -> List[Dict[str, Any]]:
        """Execute a query with caching."""
        query_hash = QueryOptimizer.generate_query_hash(query, params)
        
        # Try to get from cache
        cached_result = get_cached_query_result(query_hash)
        if cached_result is not None:
            logger.debug(f"Query cache hit: {query_hash[:8]}...")
            return cached_result
        
        # Execute query
        start_time = time.time()
        result = db.execute(text(query), params or {})
        rows = [dict(row._mapping) for row in result]
        execution_time = time.time() - start_time
        
        logger.debug(f"Query executed in {execution_time:.3f}s: {query_hash[:8]}...")
        
        # Cache result
        cache_query_result(query_hash, rows, ttl)
        
        return rows
    
    @staticmethod
    def get_slow_queries(db: Session, limit: int = 10) -> List[Dict[str, Any]]:
        """Get slow queries from PostgreSQL (if available)."""
        try:
            # This requires pg_stat_statements extension
            query = """
            SELECT 
                query,
                calls,
                total_time,
                mean_time,
                rows
            FROM pg_stat_statements 
            ORDER BY mean_time DESC 
            LIMIT :limit
            """
            return QueryOptimizer.execute_cached_query(
                db, query, {'limit': limit}, ttl=60
            )
        except Exception as e:
            logger.warning(f"Could not retrieve slow queries: {e}")
            return []
    
    @staticmethod
    def analyze_table(db: Session, table_name: str) -> Dict[str, Any]:
        """Analyze table statistics."""
        try:
            query = """
            SELECT 
                schemaname,
                tablename,
                attname,
                n_distinct,
                correlation
            FROM pg_stats 
            WHERE tablename = :table_name
            """
            stats = QueryOptimizer.execute_cached_query(
                db, query, {'table_name': table_name}, ttl=300
            )
            
            return {
                'table_name': table_name,
                'column_stats': stats,
                'analysis_time': time.time()
            }
        except Exception as e:
            logger.warning(f"Could not analyze table {table_name}: {e}")
            return {'table_name': table_name, 'error': str(e)}


class PaginationHelper:
    """Helper for efficient pagination."""
    
    @staticmethod
    def paginate_query(
        query: Query,
        page: int = 1,
        per_page: int = 20,
        max_per_page: int = 100
    ) -> Dict[str, Any]:
        """Paginate a SQLAlchemy query efficiently."""
        # Validate parameters
        page = max(1, page)
        per_page = min(max(1, per_page), max_per_page)
        
        # Calculate offset
        offset = (page - 1) * per_page
        
        # Get total count (this can be expensive for large tables)
        total = query.count()
        
        # Get items for current page
        items = query.offset(offset).limit(per_page).all()
        
        # Calculate pagination info
        total_pages = (total + per_page - 1) // per_page
        has_prev = page > 1
        has_next = page < total_pages
        
        return {
            'items': items,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'total_pages': total_pages,
                'has_prev': has_prev,
                'has_next': has_next,
                'prev_page': page - 1 if has_prev else None,
                'next_page': page + 1 if has_next else None
            }
        }
    
    @staticmethod
    def cursor_paginate(
        query: Query,
        cursor_field: str,
        cursor_value: Any = None,
        limit: int = 20,
        direction: str = 'next'
    ) -> Dict[str, Any]:
        """Cursor-based pagination for better performance on large datasets."""
        if cursor_value is not None:
            if direction == 'next':
                query = query.filter(getattr(query.column_descriptions[0]['type'], cursor_field) > cursor_value)
            else:  # previous
                query = query.filter(getattr(query.column_descriptions[0]['type'], cursor_field) < cursor_value)
        
        items = query.limit(limit + 1).all()
        
        has_more = len(items) > limit
        if has_more:
            items = items[:-1]
        
        next_cursor = None
        if items and has_more:
            next_cursor = getattr(items[-1], cursor_field)
        
        return {
            'items': items,
            'cursor': {
                'next_cursor': next_cursor,
                'has_more': has_more,
                'limit': limit
            }
        }


class DatabaseMonitor:
    """Database performance monitoring."""
    
    def __init__(self):
        self.query_stats = {}
        self.connection_stats = {
            'total_connections': 0,
            'active_connections': 0,
            'slow_queries': 0
        }
    
    def record_query(self, query: str, execution_time: float) -> None:
        """Record query execution statistics."""
        query_hash = hashlib.md5(query.encode()).hexdigest()[:8]
        
        if query_hash not in self.query_stats:
            self.query_stats[query_hash] = {
                'query': query[:100] + '...' if len(query) > 100 else query,
                'count': 0,
                'total_time': 0,
                'min_time': float('inf'),
                'max_time': 0
            }
        
        stats = self.query_stats[query_hash]
        stats['count'] += 1
        stats['total_time'] += execution_time
        stats['min_time'] = min(stats['min_time'], execution_time)
        stats['max_time'] = max(stats['max_time'], execution_time)
        
        # Track slow queries
        if execution_time > 1.0:  # Queries taking more than 1 second
            self.connection_stats['slow_queries'] += 1
            logger.warning(f"Slow query detected ({execution_time:.3f}s): {query[:100]}...")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get database performance statistics."""
        # Calculate averages
        for stats in self.query_stats.values():
            if stats['count'] > 0:
                stats['avg_time'] = stats['total_time'] / stats['count']
        
        return {
            'connection_stats': self.connection_stats,
            'query_stats': dict(list(self.query_stats.items())[:20]),  # Top 20 queries
            'total_queries': sum(stats['count'] for stats in self.query_stats.values())
        }
    
    def reset_stats(self) -> None:
        """Reset all statistics."""
        self.query_stats.clear()
        self.connection_stats = {
            'total_connections': 0,
            'active_connections': 0,
            'slow_queries': 0
        }


# Global database monitor instance
db_monitor = DatabaseMonitor()


# SQLAlchemy event listeners for monitoring
@event.listens_for(Engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """Record query start time."""
    context._query_start_time = time.time()


@event.listens_for(Engine, "after_cursor_execute")
def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """Record query execution time."""
    if hasattr(context, '_query_start_time'):
        execution_time = time.time() - context._query_start_time
        db_monitor.record_query(statement, execution_time)


@event.listens_for(Pool, "connect")
def receive_connect(dbapi_connection, connection_record):
    """Track new database connections."""
    db_monitor.connection_stats['total_connections'] += 1
    db_monitor.connection_stats['active_connections'] += 1


@event.listens_for(Pool, "checkout")
def receive_checkout(dbapi_connection, connection_record, connection_proxy):
    """Track connection checkout."""
    pass


@event.listens_for(Pool, "checkin")
def receive_checkin(dbapi_connection, connection_record):
    """Track connection checkin."""
    pass


def get_database_stats(db: Session) -> Dict[str, Any]:
    """Get comprehensive database statistics."""
    stats = {
        'monitor_stats': db_monitor.get_stats(),
        'timestamp': time.time()
    }
    
    # Add database-specific stats if available
    try:
        # PostgreSQL specific stats
        connection_info = db.execute(text("""
            SELECT 
                count(*) as total_connections,
                count(*) FILTER (WHERE state = 'active') as active_connections,
                count(*) FILTER (WHERE state = 'idle') as idle_connections
            FROM pg_stat_activity
        """)).fetchone()
        
        if connection_info:
            stats['database_connections'] = dict(connection_info._mapping)
            
    except Exception as e:
        logger.debug(f"Could not get database connection stats: {e}")
    
    return stats


def optimize_database_settings(db: Session) -> List[str]:
    """Suggest database optimization settings."""
    suggestions = []
    
    try:
        # Check current settings
        settings_query = """
        SELECT name, setting, unit, short_desc 
        FROM pg_settings 
        WHERE name IN (
            'shared_buffers',
            'effective_cache_size',
            'work_mem',
            'maintenance_work_mem',
            'max_connections'
        )
        """
        
        current_settings = QueryOptimizer.execute_cached_query(db, settings_query, ttl=3600)
        
        for setting in current_settings:
            name = setting['name']
            value = setting['setting']
            
            if name == 'shared_buffers' and int(value) < 128000:  # Less than ~1GB
                suggestions.append(f"Consider increasing shared_buffers (current: {value})")
            
            elif name == 'work_mem' and int(value) < 4096:  # Less than 4MB
                suggestions.append(f"Consider increasing work_mem (current: {value})")
            
            elif name == 'max_connections' and int(value) > 200:
                suggestions.append(f"Consider using connection pooling (current connections: {value})")
        
    except Exception as e:
        logger.debug(f"Could not analyze database settings: {e}")
        suggestions.append("Enable pg_stat_statements extension for better query analysis")
    
    return suggestions

"""
Custom middleware for the FastAPI application.
"""

import time
import uuid
from datetime import datetime
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.middleware.base import RequestResponseEndpoint
import logging

from app.core.monitoring import metrics_collector, RequestMetrics

logger = logging.getLogger("app.middleware")


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware to log all HTTP requests and responses."""

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        # Generate request ID
        request_id = str(uuid.uuid4())

        # Start timing
        start_time = time.time()

        # Log request
        logger.info(
            f"Request started - ID: {request_id} | "
            f"Method: {request.method} | "
            f"URL: {request.url} | "
            f"Client: {request.client.host if request.client else 'unknown'}"
        )

        # Add request ID to request state
        request.state.request_id = request_id

        # Increment active requests counter
        metrics_collector.increment_active_requests()

        try:
            # Process request
            response = await call_next(request)

            # Calculate processing time
            process_time = time.time() - start_time

            # Record metrics
            metrics = RequestMetrics(
                method=request.method,
                path=request.url.path,
                status_code=response.status_code,
                response_time=process_time,
                timestamp=datetime.now(),
                ip_address=request.client.host if request.client else None,
                user_agent=request.headers.get("user-agent"),
            )
            metrics_collector.record_request(metrics)

            # Log response
            logger.info(
                f"Request completed - ID: {request_id} | "
                f"Status: {response.status_code} | "
                f"Time: {process_time:.3f}s"
            )

            # Add headers
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = str(process_time)

            return response

        except Exception as e:
            # Calculate processing time
            process_time = time.time() - start_time

            # Record error metrics
            error_metrics = RequestMetrics(
                method=request.method,
                path=request.url.path,
                status_code=500,  # Internal server error
                response_time=process_time,
                timestamp=datetime.now(),
                ip_address=request.client.host if request.client else None,
                user_agent=request.headers.get("user-agent"),
            )
            metrics_collector.record_request(error_metrics)

            # Log error
            logger.error(
                f"Request failed - ID: {request_id} | "
                f"Error: {str(e)} | "
                f"Time: {process_time:.3f}s"
            )

            raise
        finally:
            # Decrement active requests counter
            metrics_collector.decrement_active_requests()


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware to add security headers to all responses."""

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        response = await call_next(request)

        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Permissions-Policy"] = (
            "geolocation=(), microphone=(), camera=()"
        )

        # Add HSTS header for HTTPS
        if request.url.scheme == "https":
            response.headers["Strict-Transport-Security"] = (
                "max-age=31536000; includeSubDomains"
            )

        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Simple in-memory rate limiting middleware."""

    def __init__(self, app, requests_per_minute: int = 60):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.requests = {}  # In production, use Redis

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        # Get client IP
        client_ip = request.client.host if request.client else "unknown"

        # Skip rate limiting for health checks
        if request.url.path in ["/health", "/", "/docs", "/redoc", "/openapi.json"]:
            return await call_next(request)

        # Current time window (minute)
        current_minute = int(time.time() // 60)

        # Clean old entries
        self.requests = {
            key: value
            for key, value in self.requests.items()
            if key[1] >= current_minute - 1
        }

        # Count requests for this IP in current minute
        key = (client_ip, current_minute)
        current_requests = self.requests.get(key, 0)

        if current_requests >= self.requests_per_minute:
            logger.warning(f"Rate limit exceeded for IP: {client_ip}")
            from fastapi import HTTPException

            raise HTTPException(
                status_code=429,
                detail="Rate limit exceeded. Please try again later.",
                headers={"Retry-After": "60"},
            )

        # Increment request count
        self.requests[key] = current_requests + 1

        response = await call_next(request)

        # Add rate limit headers
        response.headers["X-RateLimit-Limit"] = str(self.requests_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(
            self.requests_per_minute - self.requests[key]
        )
        response.headers["X-RateLimit-Reset"] = str((current_minute + 1) * 60)

        return response


class DatabaseSessionMiddleware(BaseHTTPMiddleware):
    """Middleware to ensure database sessions are properly closed."""

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        try:
            response = await call_next(request)
            return response
        except Exception as e:
            # Log database-related errors
            if "database" in str(e).lower() or "sql" in str(e).lower():
                logger.error(f"Database error in request: {str(e)}")
            raise
        finally:
            # Ensure any database sessions are cleaned up
            # This is handled by the dependency injection system
            pass

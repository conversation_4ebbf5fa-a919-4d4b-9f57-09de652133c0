"""
Global exception handlers for the FastAPI application.

This module provides centralized exception handling that automatically
converts custom application exceptions to appropriate HTTP responses.
"""

import logging
from typing import Union
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy.exc import SQLAlchemyError

from app.core.exceptions import BaseAppException, to_http_exception

logger = logging.getLogger(__name__)


async def base_app_exception_handler(request: Request, exc: BaseAppException) -> JSONResponse:
    """
    Handle all custom application exceptions.
    
    Converts BaseAppException and its subclasses to appropriate HTTP responses.
    """
    logger.warning(f"Application exception: {exc.__class__.__name__}: {exc.message}")
    
    # Convert to HTTP exception
    http_exc = to_http_exception(exc)
    
    return JSONResponse(
        status_code=http_exc.status_code,
        content=http_exc.detail,
        headers={"X-Error-Type": exc.__class__.__name__}
    )


async def sqlalchemy_exception_handler(request: Request, exc: SQLAlchemyError) -> JSONResponse:
    """
    Handle SQLAlchemy database exceptions.
    
    Provides a fallback for database errors that weren't caught by services.
    """
    logger.error(f"Unhandled database error: {str(exc)}")
    
    return JSONResponse(
        status_code=500,
        content={
            "message": "Internal database error",
            "type": "DatabaseError",
        },
        headers={"X-Error-Type": "SQLAlchemyError"}
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    Handle any unhandled exceptions.
    
    Provides a fallback for unexpected errors.
    """
    logger.error(f"Unhandled exception: {exc.__class__.__name__}: {str(exc)}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "message": "Internal server error",
            "type": "InternalError",
        },
        headers={"X-Error-Type": "UnhandledException"}
    )


def register_exception_handlers(app):
    """
    Register all exception handlers with the FastAPI application.
    
    Args:
        app: FastAPI application instance
    """
    # Register custom exception handlers
    app.add_exception_handler(BaseAppException, base_app_exception_handler)
    app.add_exception_handler(SQLAlchemyError, sqlalchemy_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
    
    logger.info("Exception handlers registered successfully")

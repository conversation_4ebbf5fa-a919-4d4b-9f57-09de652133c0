from passlib.context import <PERSON>pt<PERSON>ontext
from app.core.utils import utc_now
from datetime import timedelta
from typing import Optional, Dict, Any
from jose import jwt, JW<PERSON>rror
from app.core.config import settings
from app.core.exceptions import AuthenticationError, raise_validation_error
import logging

logger = logging.getLogger(__name__)

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a plain password against its hash."""
    try:
        return pwd_context.verify(plain_password, hashed_password)
    except Exception as e:
        logger.error(f"Password verification error: {str(e)}")
        return False


def get_password_hash(password: str) -> str:
    """Generate password hash."""
    if not password:
        raise_validation_error("Password cannot be empty")

    try:
        return pwd_context.hash(password)
    except Exception as e:
        logger.error(f"Password hashing error: {str(e)}")
        raise_validation_error("Failed to hash password")


def create_access_token(
    data: Dict[str, Any], expires_delta: Optional[timedelta] = None
) -> str:
    """Create a JWT access token."""
    if not data:
        raise_validation_error("Token data cannot be empty")

    to_encode = data.copy()

    if expires_delta:
        expire = utc_now() + expires_delta
    else:
        expire = utc_now() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire, "iat": utc_now(), "type": "access"})

    try:
        encoded_jwt = jwt.encode(
            to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
        )
        logger.debug(f"Created access token for user: {data.get('sub', 'unknown')}")
        return encoded_jwt
    except Exception as e:
        logger.error(f"Token creation error: {str(e)}")
        raise AuthenticationError("Failed to create access token")


def verify_token(token: str) -> Dict[str, Any]:
    """Verify and decode JWT token."""
    if not token:
        raise AuthenticationError("Token is required")

    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )

        # Verify token type
        if payload.get("type") != "access":
            raise AuthenticationError("Invalid token type")

        # Check if token has expired
        exp = payload.get("exp")
        if not exp or utc_now().timestamp() > exp:
            raise AuthenticationError("Token has expired")

        return payload

    except JWTError as e:
        logger.warning(f"JWT verification failed: {str(e)}")
        raise AuthenticationError("Invalid token")
    except Exception as e:
        logger.error(f"Token verification error: {str(e)}")
        raise AuthenticationError("Token verification failed")


def create_refresh_token(data: Dict[str, Any]) -> str:
    """Create a refresh token (longer expiration)."""
    if not data:
        raise_validation_error("Token data cannot be empty")

    to_encode = data.copy()
    expire = utc_now() + timedelta(days=7)  # Refresh tokens last 7 days

    to_encode.update({"exp": expire, "iat": utc_now(), "type": "refresh"})

    try:
        encoded_jwt = jwt.encode(
            to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
        )
        return encoded_jwt
    except Exception as e:
        logger.error(f"Refresh token creation error: {str(e)}")
        raise AuthenticationError("Failed to create refresh token")


def verify_refresh_token(token: str) -> Dict[str, Any]:
    """Verify refresh token."""
    if not token:
        raise AuthenticationError("Refresh token is required")

    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )

        # Verify token type
        if payload.get("type") != "refresh":
            raise AuthenticationError("Invalid refresh token type")

        return payload

    except JWTError as e:
        logger.warning(f"Refresh token verification failed: {str(e)}")
        raise AuthenticationError("Invalid refresh token")
    except Exception as e:
        logger.error(f"Refresh token verification error: {str(e)}")
        raise AuthenticationError("Refresh token verification failed")

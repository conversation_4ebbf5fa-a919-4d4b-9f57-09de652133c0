"""
Custom exception classes for the FastAPI application.

This module defines all custom exceptions used throughout the application,
providing consistent error handling and HTTP status code mapping.
"""

from typing import Any, Dict, Optional
from fastapi import HTTPException, status


class BaseAppException(Exception):
    """Base exception class for all application-specific exceptions."""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(BaseAppException):
    """Raised when data validation fails."""

    def __init__(
        self,
        message: str = "Validation failed",
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(message, details)


class NotFoundError(BaseAppException):
    """Raised when a requested resource is not found."""

    def __init__(self, resource: str, identifier: Any = None):
        if identifier:
            message = f"{resource} with identifier '{identifier}' not found"
        else:
            message = f"{resource} not found"
        super().__init__(message)


class DuplicateError(BaseAppException):
    """Raised when trying to create a resource that already exists."""

    def __init__(self, resource: str, field: str, value: Any):
        message = f"{resource} with {field} '{value}' already exists"
        super().__init__(message)


class AuthenticationError(BaseAppException):
    """Raised when authentication fails."""

    def __init__(self, message: str = "Authentication failed"):
        super().__init__(message)


class AuthorizationError(BaseAppException):
    """Raised when user lacks permission for an action."""

    def __init__(self, message: str = "Insufficient permissions"):
        super().__init__(message)


class DatabaseError(BaseAppException):
    """Raised when database operations fail."""

    def __init__(
        self,
        message: str = "Database operation failed",
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(message, details)


class BusinessLogicError(BaseAppException):
    """Raised when business logic constraints are violated."""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, details)


# HTTP Exception mapping utilities
def to_http_exception(exc: BaseAppException) -> HTTPException:
    """Convert application exception to FastAPI HTTPException."""

    status_code_map = {
        ValidationError: status.HTTP_400_BAD_REQUEST,
        NotFoundError: status.HTTP_404_NOT_FOUND,
        DuplicateError: status.HTTP_409_CONFLICT,
        AuthenticationError: status.HTTP_401_UNAUTHORIZED,
        AuthorizationError: status.HTTP_403_FORBIDDEN,
        DatabaseError: status.HTTP_500_INTERNAL_SERVER_ERROR,
        BusinessLogicError: status.HTTP_422_UNPROCESSABLE_CONTENT,
    }

    status_code = status_code_map.get(type(exc), status.HTTP_500_INTERNAL_SERVER_ERROR)

    detail = {
        "message": exc.message,
        "type": exc.__class__.__name__,
    }

    if exc.details:
        detail["details"] = exc.details

    return HTTPException(status_code=status_code, detail=detail)


def raise_not_found(resource: str, identifier: Any = None) -> None:
    """Utility function to raise NotFoundError."""
    raise NotFoundError(resource, identifier)


def raise_duplicate(resource: str, field: str, value: Any) -> None:
    """Utility function to raise DuplicateError."""
    raise DuplicateError(resource, field, value)


def raise_validation_error(
    message: str, details: Optional[Dict[str, Any]] = None
) -> None:
    """Utility function to raise ValidationError."""
    raise ValidationError(message, details)


def raise_business_logic_error(
    message: str, details: Optional[Dict[str, Any]] = None
) -> None:
    """Utility function to raise BusinessLogicError."""
    raise BusinessLogicError(message, details)

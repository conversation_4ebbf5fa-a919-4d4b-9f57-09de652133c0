"""
Application monitoring and metrics collection.
"""

import time
import threading
from collections import defaultdict, deque
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import logging

logger = logging.getLogger("app.monitoring")


@dataclass
class RequestMetrics:
    """Metrics for HTTP requests."""

    method: str
    path: str
    status_code: int
    response_time: float
    timestamp: datetime
    user_id: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None


@dataclass
class SystemMetrics:
    """System-level metrics."""

    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    active_connections: int = 0


@dataclass
class ApplicationMetrics:
    """Application-level metrics."""

    timestamp: datetime
    active_users: int = 0
    total_requests: int = 0
    error_rate: float = 0.0
    avg_response_time: float = 0.0


class MetricsCollector:
    """Centralized metrics collection system."""

    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.request_metrics: deque = deque(maxlen=max_history)
        self.system_metrics: deque = deque(maxlen=max_history)
        self.application_metrics: deque = deque(maxlen=max_history)

        # Real-time counters
        self.request_counts = defaultdict(int)
        self.error_counts = defaultdict(int)
        self.response_times = defaultdict(list)
        self.active_requests = 0

        # Thread lock for thread-safe operations
        self._lock = threading.Lock()

        logger.info("Metrics collector initialized")

    def record_request(self, metrics: RequestMetrics) -> None:
        """Record HTTP request metrics."""
        with self._lock:
            self.request_metrics.append(metrics)

            # Update counters
            key = f"{metrics.method}:{metrics.path}"
            self.request_counts[key] += 1

            if metrics.status_code >= 400:
                self.error_counts[key] += 1

            # Track response times (keep last 100 for each endpoint)
            if len(self.response_times[key]) >= 100:
                self.response_times[key].pop(0)
            self.response_times[key].append(metrics.response_time)

    def record_system_metrics(self, metrics: SystemMetrics) -> None:
        """Record system metrics."""
        with self._lock:
            self.system_metrics.append(metrics)

    def record_application_metrics(self, metrics: ApplicationMetrics) -> None:
        """Record application metrics."""
        with self._lock:
            self.application_metrics.append(metrics)

    def increment_active_requests(self) -> None:
        """Increment active request counter."""
        with self._lock:
            self.active_requests += 1

    def decrement_active_requests(self) -> None:
        """Decrement active request counter."""
        with self._lock:
            self.active_requests = max(0, self.active_requests - 1)

    def get_request_summary(self, minutes: int = 5) -> Dict[str, Any]:
        """Get request metrics summary for the last N minutes."""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)

        with self._lock:
            recent_requests = [
                req for req in self.request_metrics if req.timestamp >= cutoff_time
            ]

        if not recent_requests:
            return {
                "total_requests": 0,
                "error_rate": 0.0,
                "avg_response_time": 0.0,
                "requests_per_minute": 0.0,
                "status_codes": {},
                "top_endpoints": [],
            }

        total_requests = len(recent_requests)
        error_requests = sum(1 for req in recent_requests if req.status_code >= 400)
        error_rate = (
            (error_requests / total_requests) * 100 if total_requests > 0 else 0
        )

        avg_response_time = (
            sum(req.response_time for req in recent_requests) / total_requests
        )
        requests_per_minute = total_requests / minutes

        # Status code distribution
        status_codes = defaultdict(int)
        for req in recent_requests:
            status_codes[req.status_code] += 1

        # Top endpoints by request count
        endpoint_counts = defaultdict(int)
        for req in recent_requests:
            endpoint_counts[f"{req.method} {req.path}"] += 1

        top_endpoints = sorted(
            endpoint_counts.items(), key=lambda x: x[1], reverse=True
        )[:10]

        return {
            "total_requests": total_requests,
            "error_rate": round(error_rate, 2),
            "avg_response_time": round(avg_response_time * 1000, 2),  # Convert to ms
            "requests_per_minute": round(requests_per_minute, 2),
            "status_codes": dict(status_codes),
            "top_endpoints": [
                {"endpoint": ep, "count": count} for ep, count in top_endpoints
            ],
            "active_requests": self.active_requests,
        }

    def get_endpoint_metrics(self, endpoint: str) -> Dict[str, Any]:
        """Get detailed metrics for a specific endpoint."""
        with self._lock:
            response_times = self.response_times.get(endpoint, [])
            total_requests = self.request_counts.get(endpoint, 0)
            error_count = self.error_counts.get(endpoint, 0)

        if not response_times:
            return {
                "endpoint": endpoint,
                "total_requests": total_requests,
                "error_count": error_count,
                "error_rate": 0.0,
                "avg_response_time": 0.0,
                "min_response_time": 0.0,
                "max_response_time": 0.0,
                "p95_response_time": 0.0,
            }

        error_rate = (error_count / total_requests) * 100 if total_requests > 0 else 0
        avg_response_time = sum(response_times) / len(response_times)
        min_response_time = min(response_times)
        max_response_time = max(response_times)

        # Calculate 95th percentile
        sorted_times = sorted(response_times)
        p95_index = int(len(sorted_times) * 0.95)
        p95_response_time = sorted_times[p95_index] if sorted_times else 0

        return {
            "endpoint": endpoint,
            "total_requests": total_requests,
            "error_count": error_count,
            "error_rate": round(error_rate, 2),
            "avg_response_time": round(avg_response_time * 1000, 2),  # Convert to ms
            "min_response_time": round(min_response_time * 1000, 2),
            "max_response_time": round(max_response_time * 1000, 2),
            "p95_response_time": round(p95_response_time * 1000, 2),
        }

    def get_system_summary(self) -> Dict[str, Any]:
        """Get latest system metrics summary."""
        with self._lock:
            if not self.system_metrics:
                return {"error": "No system metrics available"}

            latest = self.system_metrics[-1]

        return {
            "timestamp": latest.timestamp.isoformat(),
            "cpu_percent": latest.cpu_percent,
            "memory_percent": latest.memory_percent,
            "disk_percent": latest.disk_percent,
            "active_connections": latest.active_connections,
        }

    def get_alerts(self) -> List[Dict[str, Any]]:
        """Get current system alerts based on thresholds."""
        alerts = []

        # Check recent request metrics
        recent_summary = self.get_request_summary(minutes=5)

        if recent_summary["error_rate"] > 10:  # More than 10% error rate
            alerts.append(
                {
                    "type": "high_error_rate",
                    "severity": "warning",
                    "message": f"High error rate: {recent_summary['error_rate']}%",
                    "value": recent_summary["error_rate"],
                    "threshold": 10,
                }
            )

        if recent_summary["avg_response_time"] > 1000:  # More than 1 second
            alerts.append(
                {
                    "type": "slow_response_time",
                    "severity": "warning",
                    "message": f"Slow response time: {recent_summary['avg_response_time']}ms",
                    "value": recent_summary["avg_response_time"],
                    "threshold": 1000,
                }
            )

        # Check system metrics
        system_summary = self.get_system_summary()
        if "error" not in system_summary:
            if system_summary["cpu_percent"] > 80:
                alerts.append(
                    {
                        "type": "high_cpu_usage",
                        "severity": "warning",
                        "message": f"High CPU usage: {system_summary['cpu_percent']}%",
                        "value": system_summary["cpu_percent"],
                        "threshold": 80,
                    }
                )

            if system_summary["memory_percent"] > 85:
                alerts.append(
                    {
                        "type": "high_memory_usage",
                        "severity": "critical",
                        "message": f"High memory usage: {system_summary['memory_percent']}%",
                        "value": system_summary["memory_percent"],
                        "threshold": 85,
                    }
                )

            if system_summary["disk_percent"] > 90:
                alerts.append(
                    {
                        "type": "high_disk_usage",
                        "severity": "critical",
                        "message": f"High disk usage: {system_summary['disk_percent']}%",
                        "value": system_summary["disk_percent"],
                        "threshold": 90,
                    }
                )

        return alerts

    def reset_metrics(self) -> None:
        """Reset all metrics (useful for testing)."""
        with self._lock:
            self.request_metrics.clear()
            self.system_metrics.clear()
            self.application_metrics.clear()
            self.request_counts.clear()
            self.error_counts.clear()
            self.response_times.clear()
            self.active_requests = 0

        logger.info("All metrics reset")


# Global metrics collector instance
metrics_collector = MetricsCollector()

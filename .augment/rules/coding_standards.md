# FastAPI Project Coding Standards

## 📁 Project Structure Rules

### Directory Organization
- **app/**: Main application code
- **core/**: Configuration, settings, and utilities
- **db/**: Database-related code (models, connections, migrations)
- **api/**: API layer (routes, dependencies)
- **schemas/**: Pydantic models for validation
- **services/**: Business logic layer
- **tests/**: Test cases

### File Naming Conventions
- Use **snake_case** for all Python files: `auth_service.py`, `todo_model.py`
- Use **lowercase** for directories: `api/routes/`, `db/models/`
- Test files should start with `test_`: `test_auth.py`, `test_todos.py`

## 🐍 Python Naming Conventions

### Variables and Functions
```python
# ✅ Good - snake_case
user_name = "john_doe"
todo_count = 5

def get_user_by_id(user_id: int):
    pass

def create_todo_item(todo_data: dict):
    pass
```

### Classes
```python
# ✅ Good - PascalCase
class UserModel:
    pass

class TodoService:
    pass

class AuthenticationError(Exception):
    pass
```

### Constants
```python
# ✅ Good - UPPER_SNAKE_CASE
SECRET_KEY = "your-secret-key"
DATABASE_URL = "sqlite:///./test.db"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
```

### Private Methods and Variables
```python
# ✅ Good - prefix with underscore
class UserService:
    def __init__(self):
        self._db_session = None
    
    def _validate_user_data(self, data: dict):
        pass
```

## 🏗️ Architecture Rules

### Layer Separation
1. **API Layer** (`api/routes/`): Handle HTTP requests/responses only
2. **Service Layer** (`services/`): Business logic and data processing
3. **Database Layer** (`db/models/`): Data models and database operations
4. **Schema Layer** (`schemas/`): Data validation and serialization

### Dependency Flow
```
API Routes → Services → Database Models
     ↓
   Schemas (for validation)
```

## 📝 Code Documentation Rules

### Function Documentation
```python
def create_user(user_data: UserCreate) -> User:
    """
    Create a new user in the database.
    
    Args:
        user_data (UserCreate): User creation data with email and password
        
    Returns:
        User: Created user object with generated ID
        
    Raises:
        ValueError: If email already exists
        DatabaseError: If database operation fails
    """
    pass
```

### Class Documentation
```python
class TodoService:
    """
    Service class for managing todo operations.
    
    This class handles all business logic related to todo items,
    including creation, updates, deletion, and retrieval.
    
    Attributes:
        db_session: Database session for operations
    """
    pass
```

### Inline Comments
```python
# Check if user already exists before creating
existing_user = db.query(User).filter(User.email == email).first()

# Hash password before storing in database
hashed_password = get_password_hash(password)

# Create JWT token with user information
token_data = {"sub": user.email, "exp": expire_time}
```

## 🔧 Function and Method Rules

### Function Naming
- Use **verbs** for functions: `get_`, `create_`, `update_`, `delete_`
- Be descriptive: `get_user_by_email()` not `get_user()`
- Use consistent prefixes:
  - `get_` for retrieval
  - `create_` for creation
  - `update_` for modification
  - `delete_` for removal
  - `validate_` for validation
  - `check_` for boolean checks

### Function Structure
```python
def create_todo_item(
    todo_data: TodoCreate,
    current_user: User,
    db: Session
) -> Todo:
    """Create a new todo item for the current user."""
    # 1. Validate input data
    if not todo_data.title.strip():
        raise ValueError("Todo title cannot be empty")
    
    # 2. Create database object
    db_todo = Todo(
        title=todo_data.title,
        description=todo_data.description,
        user_id=current_user.id
    )
    
    # 3. Save to database
    db.add(db_todo)
    db.commit()
    db.refresh(db_todo)
    
    # 4. Return result
    return db_todo
```

## 🎯 Error Handling Rules

### Custom Exceptions
```python
# Define specific exceptions in core/exceptions.py
class AuthenticationError(Exception):
    """Raised when authentication fails."""
    pass

class TodoNotFoundError(Exception):
    """Raised when todo item is not found."""
    pass
```

### Error Handling Pattern
```python
from fastapi import HTTPException, status

def get_todo_by_id(todo_id: int, user_id: int, db: Session) -> Todo:
    """Get todo by ID for specific user."""
    todo = db.query(Todo).filter(
        Todo.id == todo_id,
        Todo.user_id == user_id
    ).first()
    
    if not todo:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Todo not found"
        )
    
    return todo
```

## 📊 Database Rules

### Model Naming
```python
# ✅ Good - Singular, PascalCase
class User(Base):
    __tablename__ = "users"  # Plural table name

class Todo(Base):
    __tablename__ = "todos"
```

### Field Naming
```python
class User(Base):
    __tablename__ = "users"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Use snake_case for column names
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

## 🧪 Testing Rules

### Test File Organization
- One test file per module: `test_auth_service.py`, `test_todo_routes.py`
- Group related tests in classes: `TestUserAuthentication`, `TestTodoOperations`

### Test Function Naming
```python
def test_create_user_with_valid_data():
    """Test user creation with valid input data."""
    pass

def test_create_user_with_duplicate_email_raises_error():
    """Test that creating user with existing email raises error."""
    pass

def test_get_todos_returns_only_user_todos():
    """Test that user can only see their own todos."""
    pass
```

## 🔒 Security Rules

### Password Handling
```python
# ✅ Always hash passwords
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str) -> str:
    """Hash a password using bcrypt."""
    return pwd_context.hash(password)
```

### Environment Variables
```python
# ✅ Use environment variables for sensitive data
import os
from pydantic import BaseSettings

class Settings(BaseSettings):
    secret_key: str = os.getenv("SECRET_KEY", "dev-secret-key")
    database_url: str = os.getenv("DATABASE_URL", "sqlite:///./test.db")
    
    class Config:
        env_file = ".env"
```

## 📦 Import Rules

### Import Order
```python
# 1. Standard library imports
import os
from datetime import datetime, timedelta
from typing import Optional, List

# 2. Third-party imports
from fastapi import FastAPI, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel

# 3. Local application imports
from app.core.config import settings
from app.db.base import get_db
from app.schemas.user import UserCreate, UserResponse
```

### Import Naming
```python
# ✅ Good - Clear and descriptive
from app.services.auth_service import AuthService
from app.schemas.todo import TodoCreate, TodoUpdate, TodoResponse

# ❌ Avoid - Unclear imports
from app.services.auth_service import *
from app.schemas import todo as t
```

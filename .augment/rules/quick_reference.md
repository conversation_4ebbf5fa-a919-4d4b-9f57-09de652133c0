# Quick Reference Guide

## 🚀 Common Commands

### Development
```bash
# Start development server
uvicorn app.main:app --reload

# Run tests
pytest

# Run tests with coverage
pytest --cov=app

# Format code
black app/
isort app/

# Type checking
mypy app/
```

### Database
```bash
# Create migration
alembic revision --autogenerate -m "description"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1
```

## 📝 Code Templates

### API Route Template
```python
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from app.api.deps import get_db, get_current_user
from app.schemas.todo import TodoCreate, TodoUpdate, TodoResponse
from app.services.todo_service import TodoService
from app.db.models.user import User

router = APIRouter()

@router.post("/", response_model=TodoResponse, status_code=status.HTTP_201_CREATED)
def create_todo(
    todo_data: TodoCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new todo item."""
    service = TodoService(db)
    return service.create_todo(todo_data, current_user.id)
```

### Service Method Template
```python
def create_todo(self, todo_data: TodoCreate, user_id: int) -> Todo:
    """
    Create a new todo item for the user.
    
    Args:
        todo_data: Todo creation data
        user_id: ID of the user creating the todo
        
    Returns:
        Created todo object
        
    Raises:
        ValidationError: If todo data is invalid
    """
    # Validate input
    if not todo_data.title.strip():
        raise ValidationError("Todo title cannot be empty")
    
    # Create database object
    db_todo = Todo(
        title=todo_data.title,
        description=todo_data.description,
        user_id=user_id,
        is_completed=todo_data.is_completed,
        priority=todo_data.priority
    )
    
    # Save to database
    self.db.add(db_todo)
    self.db.commit()
    self.db.refresh(db_todo)
    
    return db_todo
```

### Test Template
```python
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.tests.conftest import TestingSessionLocal, override_get_db

client = TestClient(app)

def test_create_todo_success():
    """Test successful todo creation."""
    # Arrange
    todo_data = {
        "title": "Test Todo",
        "description": "Test description",
        "priority": "medium"
    }
    
    # Act
    response = client.post("/todos/", json=todo_data)
    
    # Assert
    assert response.status_code == 201
    data = response.json()
    assert data["title"] == todo_data["title"]
    assert data["description"] == todo_data["description"]
```

## 🔧 Naming Conventions Quick Reference

### Files and Directories
- `snake_case.py` for Python files
- `lowercase/` for directories
- `test_*.py` for test files

### Python Code
- `snake_case` for variables and functions
- `PascalCase` for classes
- `UPPER_SNAKE_CASE` for constants
- `_private_method` for private methods

### Database
- `PascalCase` for model classes: `User`, `Todo`
- `snake_case` for table names: `users`, `todos`
- `snake_case` for column names: `user_id`, `created_at`

### API
- `/lowercase-with-hyphens/` for URL paths
- `snake_case` for JSON field names

## 🎯 Common Patterns

### Error Handling
```python
from fastapi import HTTPException, status

# Not found error
raise HTTPException(
    status_code=status.HTTP_404_NOT_FOUND,
    detail="Todo not found"
)

# Validation error
raise HTTPException(
    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
    detail="Invalid todo data"
)

# Authentication error
raise HTTPException(
    status_code=status.HTTP_401_UNAUTHORIZED,
    detail="Could not validate credentials",
    headers={"WWW-Authenticate": "Bearer"}
)
```

### Database Queries
```python
# Get single record
user = db.query(User).filter(User.id == user_id).first()

# Get multiple records with pagination
todos = db.query(Todo).filter(Todo.user_id == user_id).offset(skip).limit(limit).all()

# Update record
db.query(Todo).filter(Todo.id == todo_id).update(update_data)
db.commit()

# Delete record
db.query(Todo).filter(Todo.id == todo_id).delete()
db.commit()
```

### Dependency Injection
```python
# In routes
def create_todo(
    todo_data: TodoCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    pass

# In deps.py
def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> User:
    pass
```

## 📊 Status Codes Reference

| Code | Usage | Example |
|------|-------|---------|
| 200 | Successful GET/PUT | Get todos, Update todo |
| 201 | Successful POST | Create todo, Register user |
| 204 | Successful DELETE | Delete todo |
| 400 | Bad Request | Invalid JSON format |
| 401 | Unauthorized | Missing/invalid token |
| 403 | Forbidden | Access denied |
| 404 | Not Found | Todo doesn't exist |
| 422 | Validation Error | Invalid field values |
| 500 | Server Error | Database connection failed |

## 🔒 Security Checklist

- [ ] Hash passwords with bcrypt
- [ ] Use JWT tokens with expiration
- [ ] Validate all input with Pydantic
- [ ] Check user ownership for resources
- [ ] Use HTTPS in production
- [ ] Set proper CORS origins
- [ ] Don't expose sensitive data in errors
- [ ] Use environment variables for secrets

## 🧪 Testing Checklist

- [ ] Test all API endpoints
- [ ] Test authentication flows
- [ ] Test error scenarios
- [ ] Test data validation
- [ ] Test database operations
- [ ] Test user authorization
- [ ] Achieve 80%+ code coverage
- [ ] Use meaningful test names

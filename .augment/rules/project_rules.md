# FastAPI Todo Project - Specific Rules

## 🎯 Project-Specific Guidelines

### API Route Patterns
All API routes should follow RESTful conventions:

```python
# Authentication routes (prefix: /auth)
POST   /auth/register     # User registration
POST   /auth/login        # User login
GET    /auth/me          # Get current user profile
POST   /auth/refresh     # Refresh JWT token

# Todo routes (prefix: /todos)
GET    /todos/           # Get all todos for user
POST   /todos/           # Create new todo
GET    /todos/{id}       # Get specific todo
PUT    /todos/{id}       # Update todo
DELETE /todos/{id}       # Delete todo
```

### Response Format Standards
All API responses should follow consistent format:

```python
# Success Response
{
    "success": true,
    "data": {...},
    "message": "Operation completed successfully"
}

# Error Response
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "Invalid input data",
        "details": {...}
    }
}
```

### Authentication Flow
1. User registers with email/password
2. User logs in to receive JWT token
3. Token must be included in Authorization header: `Bearer <token>`
4. Protected routes validate token and extract user information

### Database Schema Rules

#### User Model Requirements
```python
class User(Base):
    __tablename__ = "users"
    
    # Required fields
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    
    # Optional fields
    full_name = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    todos = relationship("Todo", back_populates="owner")
```

#### Todo Model Requirements
```python
class Todo(Base):
    __tablename__ = "todos"
    
    # Required fields
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Optional fields
    description = Column(Text, nullable=True)
    is_completed = Column(Boolean, default=False)
    priority = Column(String, default="medium")  # low, medium, high
    due_date = Column(DateTime, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    owner = relationship("User", back_populates="todos")
```

### Pydantic Schema Patterns

#### Base Schemas
```python
# Base schema with common fields
class TodoBase(BaseModel):
    title: str
    description: Optional[str] = None
    is_completed: bool = False
    priority: str = "medium"
    due_date: Optional[datetime] = None

# Schema for creating todos
class TodoCreate(TodoBase):
    pass

# Schema for updating todos
class TodoUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    is_completed: Optional[bool] = None
    priority: Optional[str] = None
    due_date: Optional[datetime] = None

# Schema for API responses
class TodoResponse(TodoBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
```

### Service Layer Patterns

#### Service Class Structure
```python
class TodoService:
    """Service for managing todo operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_todo(self, todo_data: TodoCreate, user_id: int) -> Todo:
        """Create a new todo for the user."""
        # Implementation here
        pass
    
    def get_user_todos(self, user_id: int, skip: int = 0, limit: int = 100) -> List[Todo]:
        """Get all todos for a specific user."""
        # Implementation here
        pass
    
    def get_todo_by_id(self, todo_id: int, user_id: int) -> Optional[Todo]:
        """Get a specific todo by ID for the user."""
        # Implementation here
        pass
    
    def update_todo(self, todo_id: int, todo_data: TodoUpdate, user_id: int) -> Optional[Todo]:
        """Update a todo for the user."""
        # Implementation here
        pass
    
    def delete_todo(self, todo_id: int, user_id: int) -> bool:
        """Delete a todo for the user."""
        # Implementation here
        pass
```

### Error Handling Standards

#### Custom Exception Classes
```python
# In core/exceptions.py
class TodoAppException(Exception):
    """Base exception for todo application."""
    pass

class AuthenticationError(TodoAppException):
    """Authentication related errors."""
    pass

class AuthorizationError(TodoAppException):
    """Authorization related errors."""
    pass

class TodoNotFoundError(TodoAppException):
    """Todo item not found."""
    pass

class ValidationError(TodoAppException):
    """Data validation errors."""
    pass
```

#### HTTP Status Code Usage
- `200 OK`: Successful GET, PUT operations
- `201 Created`: Successful POST operations
- `204 No Content`: Successful DELETE operations
- `400 Bad Request`: Invalid input data
- `401 Unauthorized`: Missing or invalid authentication
- `403 Forbidden`: User doesn't have permission
- `404 Not Found`: Resource doesn't exist
- `422 Unprocessable Entity`: Validation errors
- `500 Internal Server Error`: Server errors

### Testing Requirements

#### Test Coverage Requirements
- Minimum 80% code coverage
- All API endpoints must have tests
- All service methods must have tests
- Database models should have basic tests

#### Test Data Patterns
```python
# Use factories for test data
class UserFactory:
    @staticmethod
    def create_user_data():
        return {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "full_name": "Test User"
        }

class TodoFactory:
    @staticmethod
    def create_todo_data():
        return {
            "title": "Test Todo",
            "description": "Test description",
            "priority": "medium"
        }
```

### Configuration Management

#### Environment Variables
```python
# Required environment variables
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///./test.db
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Optional environment variables
DEBUG=True
CORS_ORIGINS=["http://localhost:3000"]
```

#### Settings Class
```python
class Settings(BaseSettings):
    # Security
    secret_key: str
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # Database
    database_url: str
    
    # Application
    debug: bool = False
    cors_origins: List[str] = []
    
    class Config:
        env_file = ".env"
```

### Dependency Injection Patterns

#### Common Dependencies
```python
# Database dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Current user dependency
def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> User:
    # Token validation and user retrieval
    pass

# Service dependencies
def get_todo_service(db: Session = Depends(get_db)) -> TodoService:
    return TodoService(db)

def get_auth_service(db: Session = Depends(get_db)) -> AuthService:
    return AuthService(db)
```

### Logging Standards

#### Logging Configuration
```python
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

logger = logging.getLogger(__name__)

# Usage in services
def create_todo(self, todo_data: TodoCreate, user_id: int) -> Todo:
    logger.info(f"Creating todo for user {user_id}: {todo_data.title}")
    # Implementation
    logger.info(f"Todo created successfully with ID: {todo.id}")
```

### Performance Guidelines

#### Database Query Optimization
- Use `select_related()` for foreign key relationships
- Implement pagination for list endpoints
- Add database indexes for frequently queried fields
- Use connection pooling for production

#### Caching Strategy
- Cache user sessions
- Cache frequently accessed todo lists
- Use Redis for production caching

### Security Checklist

#### Authentication Security
- ✅ Hash passwords with bcrypt
- ✅ Use JWT tokens with expiration
- ✅ Validate tokens on protected routes
- ✅ Implement token refresh mechanism

#### Data Security
- ✅ Validate all input data with Pydantic
- ✅ Use parameterized queries (SQLAlchemy ORM)
- ✅ Implement proper CORS settings
- ✅ Use HTTPS in production

#### Authorization Rules
- ✅ Users can only access their own todos
- ✅ Validate user ownership before operations
- ✅ Implement proper error messages without data leakage

# Exception Handling Guide

This guide explains the comprehensive exception handling system implemented in this FastAPI application.

## 🎯 Overview

The exception handling system provides:
- **Consistent error responses** across all endpoints
- **Automatic HTTP status code mapping** from business exceptions
- **Comprehensive logging** for debugging and monitoring
- **Reusable base service** with built-in error handling
- **Type-safe exception handling** with proper error details

## 📁 Architecture

```
app/
├── core/
│   ├── exceptions.py          # Custom exception classes
│   └── exception_handlers.py  # Global exception handlers
├── services/
│   ├── base.py               # BaseService with exception handling
│   ├── admin_service.py      # Example service using BaseService
│   └── example_service.py    # Comprehensive examples
```

## 🔧 Core Components

### 1. Custom Exception Classes (`app/core/exceptions.py`)

```python
# Base exception for all application errors
class BaseAppException(Exception):
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None)

# Specific exception types
class ValidationError(BaseAppException)      # 400 Bad Request
class NotFoundError(BaseAppException)        # 404 Not Found
class DuplicateError(BaseAppException)       # 409 Conflict
class AuthenticationError(BaseAppException)  # 401 Unauthorized
class AuthorizationError(BaseAppException)   # 403 Forbidden
class DatabaseError(BaseAppException)        # 500 Internal Server Error
class BusinessLogicError(BaseAppException)   # 422 Unprocessable Entity
```

### 2. BaseService (`app/services/base.py`)

Provides common CRUD operations with built-in exception handling:

```python
class BaseService(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    def get(self, id: int) -> Optional[ModelType]
    def get_or_404(self, id: int) -> ModelType
    def get_all(self, skip: int = 0, limit: int = 100) -> List[ModelType]
    def create(self, obj_data: CreateSchemaType) -> ModelType
    def update(self, id: int, obj_data: UpdateSchemaType) -> ModelType
    def delete(self, id: int) -> bool
    def exists(self, id: int) -> bool
    def count(self) -> int
```

### 3. Global Exception Handlers (`app/core/exception_handlers.py`)

Automatically converts exceptions to HTTP responses:

```python
# Registered in main.py
register_exception_handlers(app)
```

## 🚀 Usage Examples

### Creating a Service

```python
from app.services.base import BaseService
from app.core.exceptions import raise_validation_error, raise_duplicate

class UserService(BaseService[User, UserCreate, UserUpdate]):
    def __init__(self, db: Session):
        super().__init__(User, db)
    
    def create_user(self, user_data: UserCreate) -> User:
        # Validation
        if len(user_data.password) < 8:
            raise_validation_error("Password must be at least 8 characters")
        
        # Check duplicates
        existing = self.get_by_email(user_data.email)
        if existing:
            raise_duplicate("User", "email", user_data.email)
        
        # Use base service (handles database errors automatically)
        return self.create(user_data)
```

### API Route

```python
@router.post("/users/", response_model=UserResponse)
def create_user(user: UserCreate, db: Session = Depends(get_db)):
    """Create user - exceptions are automatically handled."""
    service = UserService(db)
    return service.create_user(user)  # Exceptions become HTTP responses
```

## 📋 Exception Types and Usage

### ValidationError
Use for input validation failures:

```python
from app.core.exceptions import raise_validation_error

# Simple validation
if not email or "@" not in email:
    raise_validation_error("Invalid email format")

# With details
raise_validation_error("Invalid data", {
    "field": "email",
    "value": email,
    "expected": "valid email address"
})
```

### NotFoundError
Use when resources don't exist:

```python
from app.core.exceptions import raise_not_found

# Simple usage
user = get_user(user_id)
if not user:
    raise_not_found("User", user_id)

# BaseService provides get_or_404() that does this automatically
user = service.get_or_404(user_id)  # Raises NotFoundError if not found
```

### DuplicateError
Use for unique constraint violations:

```python
from app.core.exceptions import raise_duplicate

existing_user = get_user_by_email(email)
if existing_user:
    raise_duplicate("User", "email", email)
```

### BusinessLogicError
Use for business rule violations:

```python
from app.core.exceptions import raise_business_logic_error

if user.role == "admin" and action == "delete_self":
    raise_business_logic_error(
        "Admin cannot delete their own account",
        {"user_id": user.id, "action": action}
    )
```

## 🔄 HTTP Response Format

All exceptions are automatically converted to consistent HTTP responses:

```json
{
  "message": "User with email '<EMAIL>' already exists",
  "type": "DuplicateError",
  "details": {
    "field": "email",
    "value": "<EMAIL>"
  }
}
```

## 📊 Status Code Mapping

| Exception Type | HTTP Status | Description |
|---------------|-------------|-------------|
| ValidationError | 400 | Bad Request - Invalid input |
| AuthenticationError | 401 | Unauthorized - Authentication failed |
| AuthorizationError | 403 | Forbidden - Insufficient permissions |
| NotFoundError | 404 | Not Found - Resource doesn't exist |
| DuplicateError | 409 | Conflict - Resource already exists |
| BusinessLogicError | 422 | Unprocessable Entity - Business rule violation |
| DatabaseError | 500 | Internal Server Error - Database issues |

## 🛠️ Best Practices

### 1. Use Specific Exceptions
```python
# ✅ Good - Specific exception
raise_validation_error("Email format is invalid")

# ❌ Bad - Generic exception
raise Exception("Something went wrong")
```

### 2. Provide Helpful Details
```python
# ✅ Good - With context
raise_business_logic_error(
    "Cannot delete user with active orders",
    {"user_id": user_id, "active_orders": order_count}
)

# ❌ Bad - No context
raise_business_logic_error("Cannot delete user")
```

### 3. Use BaseService Methods
```python
# ✅ Good - Uses built-in error handling
user = service.get_or_404(user_id)
new_user = service.create(user_data)

# ❌ Bad - Manual error handling
user = db.query(User).filter(User.id == user_id).first()
if not user:
    raise HTTPException(status_code=404, detail="User not found")
```

### 4. Handle Database Errors
```python
# ✅ Good - BaseService handles this automatically
try:
    return self.create(user_data)
except BaseAppException:
    raise  # Re-raise application exceptions
except Exception as e:
    self._handle_database_error(e, "create_user")
```

## 🧪 Testing Exception Handling

```python
import pytest
from app.core.exceptions import ValidationError, NotFoundError

def test_create_user_validation():
    with pytest.raises(ValidationError) as exc_info:
        service.create_user(invalid_data)
    
    assert "Password must be at least 8 characters" in str(exc_info.value)

def test_get_nonexistent_user():
    with pytest.raises(NotFoundError):
        service.get_or_404(999999)
```

## 🔍 Logging and Monitoring

The system provides comprehensive logging:

```python
# Automatic logging in BaseService
logger.info(f"Creating new {self.model_name}")
logger.warning(f"{self.model_name} with ID {id} not found")
logger.error(f"Database error during {operation}: {str(error)}")
```

## 🎉 Benefits

1. **Consistency**: All services use the same error handling patterns
2. **Maintainability**: Centralized exception handling logic
3. **Developer Experience**: Clear error messages and proper HTTP status codes
4. **Debugging**: Comprehensive logging for troubleshooting
5. **Type Safety**: Proper exception types with IDE support
6. **Reusability**: BaseService can be extended for any model

## 🔄 Migration Guide

To migrate existing services:

1. **Inherit from BaseService**:
   ```python
   class YourService(BaseService[YourModel, CreateSchema, UpdateSchema]):
   ```

2. **Replace manual error handling**:
   ```python
   # Before
   obj = db.query(Model).filter(Model.id == id).first()
   if not obj:
       raise HTTPException(status_code=404, detail="Not found")
   
   # After
   obj = self.get_or_404(id)
   ```

3. **Use exception utilities**:
   ```python
   # Before
   if existing:
       raise HTTPException(status_code=409, detail="Already exists")
   
   # After
   if existing:
       raise_duplicate("Resource", "field", value)
   ```

This exception handling system makes your FastAPI application more robust, maintainable, and user-friendly! 🚀

# Git
.git
.gitignore
README.md
DEPLOYMENT.md
EXCEPTION_HANDLING_GUIDE.md

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.pytest_cache
nosetests.xml
coverage.xml
*.cover
*.log
.cache
.mypy_cache
.dmypy.json
dmypy.json

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Database
*.db
*.sqlite3

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Nginx
nginx*.conf

# SSL certificates
ssl/

# Backup files
*.bak
*.backup

# Temporary files
tmp/
temp/
.tmp/

# Node modules (if any)
node_modules/

# Documentation
docs/
*.md

# Test files
tests/
test_*
*_test.py

# Development files
.env.example
.env.local
.env.development

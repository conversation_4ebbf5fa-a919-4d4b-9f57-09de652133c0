# FastAPI Production Deployment Guide

This guide covers deploying the FastAPI application to production using Docker and Docker Compose.

## Prerequisites

- <PERSON><PERSON> and Docker Compose installed
- Domain name configured (for production)
- SSL certificates (for HTTPS)
- PostgreSQL database (can be containerized or external)
- Redis instance (optional, for caching)

## Environment Configuration

### 1. Create Production Environment File

Create a `.env.prod` file with production settings:

```bash
# Application
PROJECT_NAME=FastAPI Demo
VERSION=1.0.0
DEBUG=False
SECRET_KEY=your-super-secure-secret-key-at-least-32-characters-long
LOG_LEVEL=INFO

# Database
POSTGRES_DB=fastapi_db
POSTGRES_USER=fastapi_user
POSTGRES_PASSWORD=your-secure-database-password
DATABASE_URL=postgresql+psycopg2://fastapi_user:your-secure-database-password@db:5432/fastapi_db

# Redis
REDIS_URL=redis://redis:6379/0

# Security
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Rate Limiting
RATE_LIMIT_ENABLED=True
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_PERIOD=60

# Monitoring
GRAFANA_PASSWORD=your-grafana-password
```

### 2. SSL Certificates

Place your SSL certificates in the `ssl/` directory:
```
ssl/
├── cert.pem
└── key.pem
```

For Let's Encrypt certificates:
```bash
# Install certbot
sudo apt-get install certbot

# Generate certificates
sudo certbot certonly --standalone -d yourdomain.com -d www.yourdomain.com

# Copy certificates
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem ssl/cert.pem
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem ssl/key.pem
```

## Deployment Steps

### 1. Development Deployment

For local development with Docker:

```bash
# Build and start services
docker-compose up -d

# View logs
docker-compose logs -f app

# Run database migrations
docker-compose exec app alembic upgrade head

# Create initial admin user (optional)
docker-compose exec app python -c "
from app.db.base import SessionLocal
from app.services.admin_service import AdminService
from app.schemas.admin import AdminCreate

db = SessionLocal()
admin_service = AdminService(db)
admin_data = AdminCreate(
    first_name='Admin',
    last_name='User',
    email='<EMAIL>',
    password='admin123'
)
admin_service.create_admin(admin_data)
db.close()
"
```

### 2. Production Deployment

For production deployment:

```bash
# Load environment variables
export $(cat .env.prod | xargs)

# Build and start production services
docker-compose -f docker-compose.prod.yml up -d

# Run database migrations
docker-compose -f docker-compose.prod.yml exec app alembic upgrade head

# Check service health
docker-compose -f docker-compose.prod.yml ps
```

### 3. Database Migrations

```bash
# Create a new migration
docker-compose exec app alembic revision --autogenerate -m "Description of changes"

# Apply migrations
docker-compose exec app alembic upgrade head

# Rollback migration
docker-compose exec app alembic downgrade -1
```

## Monitoring and Maintenance

### 1. Health Checks

The application includes comprehensive health checks:

- Basic health: `GET /health`
- Detailed health: `GET /api/v1/health/detailed`
- Readiness probe: `GET /api/v1/health/readiness`
- Liveness probe: `GET /api/v1/health/liveness`

### 2. Metrics and Monitoring

Access monitoring dashboards:

- Application metrics: `GET /api/v1/metrics`
- Prometheus: `http://localhost:9090` (if enabled)
- Grafana: `http://localhost:3000` (if enabled)

### 3. Log Management

View application logs:

```bash
# Application logs
docker-compose logs -f app

# Database logs
docker-compose logs -f db

# Nginx logs
docker-compose logs -f nginx

# All logs
docker-compose logs -f
```

### 4. Backup and Recovery

#### Database Backup

```bash
# Create backup
docker-compose exec db pg_dump -U postgres fastapi_db > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore backup
docker-compose exec -T db psql -U postgres fastapi_db < backup_file.sql
```

#### Redis Backup

```bash
# Create Redis backup
docker-compose exec redis redis-cli BGSAVE

# Copy backup file
docker cp fastapi_redis:/data/dump.rdb ./redis_backup_$(date +%Y%m%d_%H%M%S).rdb
```

## Security Considerations

### 1. Environment Variables

- Never commit `.env` files to version control
- Use strong, unique passwords
- Rotate secrets regularly
- Use environment-specific configurations

### 2. Network Security

- Use HTTPS in production
- Configure proper CORS origins
- Implement rate limiting
- Use security headers

### 3. Database Security

- Use strong database passwords
- Limit database access
- Enable SSL for database connections
- Regular security updates

### 4. Container Security

- Use non-root users in containers
- Keep base images updated
- Scan images for vulnerabilities
- Limit container resources

## Performance Optimization

### 1. Application Performance

- Use connection pooling
- Implement caching
- Optimize database queries
- Monitor response times

### 2. Infrastructure Performance

- Use load balancing
- Configure proper resource limits
- Monitor system resources
- Scale horizontally when needed

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   ```bash
   # Check database status
   docker-compose exec db pg_isready -U postgres
   
   # Check connection from app
   docker-compose exec app python -c "
   from app.db.base import engine
   print(engine.execute('SELECT 1').scalar())
   "
   ```

2. **Redis Connection Issues**
   ```bash
   # Check Redis status
   docker-compose exec redis redis-cli ping
   ```

3. **SSL Certificate Issues**
   ```bash
   # Check certificate validity
   openssl x509 -in ssl/cert.pem -text -noout
   ```

4. **Performance Issues**
   ```bash
   # Check resource usage
   docker stats
   
   # Check application metrics
   curl http://localhost:8000/api/v1/metrics
   ```

### Log Analysis

```bash
# Check for errors
docker-compose logs app | grep ERROR

# Monitor real-time logs
docker-compose logs -f --tail=100 app

# Check Nginx access logs
docker-compose exec nginx tail -f /var/log/nginx/access.log
```

## Scaling

### Horizontal Scaling

To scale the application:

```bash
# Scale app service
docker-compose -f docker-compose.prod.yml up -d --scale app=3

# Use load balancer
# Update nginx.prod.conf upstream section with multiple servers
```

### Vertical Scaling

Update resource limits in `docker-compose.prod.yml`:

```yaml
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 1G
    reservations:
      cpus: '1.0'
      memory: 512M
```

## Maintenance

### Regular Tasks

1. **Update Dependencies**
   ```bash
   # Update requirements.txt
   # Rebuild images
   docker-compose build --no-cache
   ```

2. **Clean Up**
   ```bash
   # Remove unused images
   docker image prune -f
   
   # Remove unused volumes
   docker volume prune -f
   ```

3. **Security Updates**
   ```bash
   # Update base images
   docker-compose pull
   docker-compose up -d
   ```

## Support

For issues and questions:

- Check application logs
- Review health check endpoints
- Monitor system metrics
- Consult this deployment guide

Remember to test all changes in a staging environment before deploying to production.
